"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9482],{77690:function(n,t,e){e.d(t,{ts:function(){return m}});var r=e(45008),s=e(34206);function i(){let n=(0,r._)(['\n  mutation LoginUser($username: String!, $password: String!) {\n    login(input: {\n      clientMutationId: "login"\n      username: $username\n      password: $password\n    }) {\n      authToken\n      refreshToken\n      user {\n        id\n        databaseId\n        email\n        firstName\n        lastName\n      }\n    }\n  }\n']);return i=function(){return n},n}function o(){let n=(0,r._)(["\n  mutation RegisterUser($input: RegisterCustomerInput!) {\n    registerCustomer(input: $input) {\n      clientMutationId\n      authToken\n      refreshToken\n      customer {\n        id\n        databaseId\n        email\n        firstName\n        lastName\n      }\n    }\n  }\n"]);return o=function(){return n},n}function a(){let n=(0,r._)(["\n  mutation RefreshAuthToken($input: RefreshJwtAuthTokenInput!) {\n    refreshJwtAuthToken(input: $input) {\n      authToken\n    }\n  }\n"]);return a=function(){return n},n}function u(){let n=(0,r._)(["\n  query GetCustomer {\n    customer {\n      id\n      databaseId\n      email\n      firstName\n      lastName\n      billing {\n        firstName\n        lastName\n        company\n        address1\n        address2\n        city\n        state\n        postcode\n        country\n        email\n        phone\n      }\n      shipping {\n        firstName\n        lastName\n        company\n        address1\n        address2\n        city\n        state\n        postcode\n        country\n      }\n      orders {\n        nodes {\n          id\n          databaseId\n          date\n          status\n          total\n          lineItems {\n            nodes {\n              product {\n                node {\n                  id\n                  name\n                }\n              }\n              quantity\n              total\n            }\n          }\n        }\n      }\n    }\n  }\n"]);return u=function(){return n},n}function c(){let n=(0,r._)(["\n  mutation UpdateCustomer($input: UpdateCustomerInput!) {\n    updateCustomer(input: $input) {\n      clientMutationId\n      customer {\n        id\n        databaseId\n        email\n        firstName\n        lastName\n        displayName\n        billing {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n          email\n          phone\n        }\n        shipping {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n        }\n      }\n    }\n  }\n"]);return c=function(){return n},n}e(14474),(0,s.Ps)(i()),(0,s.Ps)(o()),(0,s.Ps)(a()),(0,s.Ps)(u()),(0,s.Ps)(c());let d="https://maroon-lapwing-781450.hostingersite.com/graphql",l=d&&!d.startsWith("http")?"https://".concat(d):d;async function m(){try{let n=await fetch("/api/auth/user",{method:"GET",headers:{"Content-Type":"application/json"}});if(401===n.status)return null;let t=await n.json();if(!n.ok||!t.success)return null;return t.user}catch(n){return console.error("Get user error:",n),null}}new s.g6(l,{headers:{"Content-Type":"application/json"}})},70597:function(n,t,e){e.d(t,{EJ:function(){return s},J6:function(){return r}});let r="₹",s="INR"},86366:function(n,t,e){e.d(t,{Cc:function(){return u},OR:function(){return c},Q5:function(){return a},iK:function(){return o}});var r=e(2265);class s{on(n,t){return this.listeners.has(n)||this.listeners.set(n,new Set),this.listeners.get(n).add(t),()=>{this.off(n,t)}}off(n,t){let e=this.listeners.get(n);e&&(e.delete(t),0===e.size&&this.listeners.delete(n))}emit(n,t){let e=this.listeners.get(n);e&&e.forEach(e=>{try{e(t)}catch(t){console.error("Error in event listener for ".concat(n,":"),t)}})}once(n,t){let e=r=>{t(r),this.off(n,e)};this.on(n,e)}removeAllListeners(n){n?this.listeners.delete(n):this.listeners.clear()}listenerCount(n){var t;return(null===(t=this.listeners.get(n))||void 0===t?void 0:t.size)||0}eventNames(){return Array.from(this.listeners.keys())}constructor(){this.listeners=new Map}}let i=new s,o={loginSuccess:(n,t)=>i.emit("auth:login-success",{user:n,token:t}),loginError:n=>i.emit("auth:login-error",{error:n}),logout:()=>i.emit("auth:logout",void 0),registerSuccess:(n,t)=>i.emit("auth:register-success",{user:n,token:t}),registerError:n=>i.emit("auth:register-error",{error:n}),profileUpdated:n=>i.emit("auth:profile-updated",{user:n}),sessionExpired:()=>i.emit("auth:session-expired",void 0)},a={itemAdded:(n,t)=>i.emit("cart:item-added",{item:n,message:t}),itemRemoved:(n,t)=>i.emit("cart:item-removed",{itemId:n,message:t}),itemUpdated:(n,t,e)=>i.emit("cart:item-updated",{itemId:n,quantity:t,message:e}),cleared:n=>i.emit("cart:cleared",{message:n}),checkoutSuccess:(n,t)=>i.emit("cart:checkout-success",{orderId:n,message:t}),checkoutError:n=>i.emit("cart:checkout-error",{error:n}),syncStarted:()=>i.emit("cart:sync-started",void 0),syncCompleted:()=>i.emit("cart:sync-completed",void 0)},u={show:function(n){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info",e=arguments.length>2?arguments[2]:void 0;return i.emit("notification:show",{message:n,type:t,duration:e})},hide:n=>i.emit("notification:hide",{id:n})};function c(n,t){let e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];(0,r.useEffect)(()=>i.on(n,t),e)}}}]);