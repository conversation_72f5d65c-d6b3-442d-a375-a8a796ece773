"use strict";(()=>{var e={};e.id=5835,e.ids=[5835],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},22740:(e,o,r)=>{r.r(o),r.d(o,{originalPathname:()=>b,patchFetch:()=>y,requestAsyncStorage:()=>k,routeModule:()=>S,serverHooks:()=>f,staticGenerationAsyncStorage:()=>w});var t={};r.r(t),r.d(t,{GET:()=>u,POST:()=>p});var s=r(49303),n=r(88716),a=r(60670),c=r(87070),i=r(94868);let d=process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN?new i.s({url:process.env.UPSTASH_REDIS_REST_URL,token:process.env.UPSTASH_REDIS_REST_TOKEN}):null,l={PRODUCTS:86400};async function u(){return c.NextResponse.json({message:"WooCommerce Order Webhook Endpoint",status:"active",timestamp:new Date().toISOString()})}async function p(e){console.log("=== WooCommerce Order Webhook Received ===");try{let o,r;let t=Object.fromEntries(e.headers.entries());console.log("Headers:",t);try{o=await e.text(),console.log("Body received, length:",o.length),console.log("Body preview:",o.substring(0,200))}catch(e){return console.error("Error reading request body:",e),c.NextResponse.json({error:"Failed to read request body",details:e instanceof Error?e.message:"Unknown error"},{status:400})}if(!o||0===o.length)return console.error("Empty request body received"),c.NextResponse.json({error:"Empty request body"},{status:400});let s=e.headers.get("content-type")||"";if(console.log("Content-Type:",s),s.includes("application/x-www-form-urlencoded")){let e=new URLSearchParams(o);if(r=Object.fromEntries(e.entries()),console.log("Parsed form data:",r),r.webhook_id)return console.log("Received order webhook test ping, webhook_id:",r.webhook_id),c.NextResponse.json({success:!0,message:"Order webhook test ping received successfully",webhook_id:r.webhook_id,timestamp:new Date().toISOString()})}else try{r=JSON.parse(o),console.log("JSON parsed successfully")}catch(e){return console.error("JSON parse error:",e),c.NextResponse.json({error:"Invalid JSON in request body",details:e instanceof Error?e.message:"Unknown error"},{status:400})}return console.log("Order webhook data received:",{id:r?.id,number:r?.number,status:r?.status,line_items:r?.line_items?.length||0}),await g(r),console.log("=== Order Webhook Processing Complete ==="),c.NextResponse.json({success:!0,message:"Order webhook processed successfully",orderId:r.id,timestamp:new Date().toISOString()})}catch(e){return console.error("=== Order Webhook Processing Error ==="),console.error("Error details:",e),console.error("Stack trace:",e instanceof Error?e.stack:"No stack trace"),c.NextResponse.json({error:"Order webhook processing failed",details:e instanceof Error?e.message:"Unknown error",timestamp:new Date().toISOString()},{status:500})}}async function g(e){try{if(!e||!e.id){console.error("Invalid order data received:",e);return}let o=e.id.toString(),r=e.status,t=e.line_items||[];for(let e of(console.log(`Processing order update for order ${o} (Status: ${r})`),console.log("Line items count:",t.length),t)){let t=e.product_id?.toString(),s=e.variation_id?.toString(),n=e.quantity||0;if(!t)continue;console.log(`Processing line item: Product ${t}, Variation ${s}, Quantity ${n}`);let a=0,c="";switch(r){case"processing":case"completed":a=-n,c="Order processed";break;case"cancelled":case"refunded":case"failed":a=n,c="Order cancelled/refunded";break;default:console.log(`No stock change needed for order status: ${r}`);continue}0!==a&&(await m(t,s,a,c),await h(t,{stockChange:a,changeReason:c,orderId:o,orderStatus:r,timestamp:new Date().toISOString()}))}}catch(e){console.error("Error handling order update:",e)}}async function m(e,o,r,t){if(!d){console.log("Redis not available, skipping cache update");return}try{let o=`product:${e}`,s=await d.get(o);if(s&&"object"==typeof s){let n={...s,_lastStockChange:{change:r,reason:t,timestamp:new Date().toISOString()}};await d.set(o,n,l.PRODUCTS),console.log(`Updated cache for product ${e}: ${t} (${r>0?"+":""}${r})`)}}catch(e){console.error("Error updating product stock cache:",e)}}async function h(e,o){if(!d){console.log("Redis not available, skipping broadcast");return}try{await d.set(`stock_update:${e}`,{...o,productId:e,timestamp:Date.now()},60),console.log("Broadcasted stock update for product:",e)}catch(e){console.error("Failed to broadcast stock update:",e)}}let S=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/webhooks/order/route",pathname:"/api/webhooks/order",filename:"route",bundlePath:"app/api/webhooks/order/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\webhooks\\order\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:k,staticGenerationAsyncStorage:w,serverHooks:f}=S,b="/api/webhooks/order/route";function y(){return(0,a.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:w})}}};var o=require("../../../../webpack-runtime.js");o.C(e);var r=e=>o(o.s=e),t=o.X(0,[8948,5972,4766,4868],()=>r(22740));module.exports=t})();