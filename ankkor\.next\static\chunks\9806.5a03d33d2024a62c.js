"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9806],{17325:function(r,n,e){e.d(n,{q:function(){return c}});var t=e(2265);function u(r,n){return"function"==typeof r?r(n):r&&(r.current=n),r}var o="undefined"!=typeof window?t.useLayoutEffect:t.useEffect,i=new WeakMap;function c(r,n){var e,c,a,f=(e=n||null,c=function(n){return r.forEach(function(r){return u(r,n)})},(a=(0,t.useState)(function(){return{value:e,callback:c,facade:{get current(){return a.value},set current(value){var r=a.value;r!==value&&(a.value=value,a.callback(value,r))}}}})[0]).callback=c,a.facade);return o(function(){var n=i.get(f);if(n){var e=new Set(n),t=new Set(r),o=f.current;e.forEach(function(r){t.has(r)||u(r,null)}),t.forEach(function(r){e.has(r)||u(r,o)})}i.set(f,r)},[r]),f}},49085:function(r,n,e){e.d(n,{L:function(){return i}});var t=e(5853),u=e(2265),o=function(r){var n=r.sideCar,e=(0,t._T)(r,["sideCar"]);if(!n)throw Error("Sidecar: please provide `sideCar` property to import the right car");var o=n.read();if(!o)throw Error("Sidecar medium not found");return u.createElement(o,(0,t.pi)({},e))};function i(r,n){return r.useMedium(n),o}o.isSideCarExport=!0},31412:function(r,n,e){e.d(n,{_:function(){return o}});var t=e(5853);function u(r){return r}function o(r){void 0===r&&(r={});var n,e,o,i=(void 0===n&&(n=u),e=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return e.length?e[e.length-1]:null},useMedium:function(r){var t=n(r,o);return e.push(t),function(){e=e.filter(function(r){return r!==t})}},assignSyncMedium:function(r){for(o=!0;e.length;){var n=e;e=[],n.forEach(r)}e={push:function(n){return r(n)},filter:function(){return e}}},assignMedium:function(r){o=!0;var n=[];if(e.length){var t=e;e=[],t.forEach(r),n=e}var u=function(){var e=n;n=[],e.forEach(r)},i=function(){return Promise.resolve().then(u)};i(),e={push:function(r){n.push(r),i()},filter:function(r){return n=n.filter(r),e}}}});return i.options=(0,t.pi)({async:!0,ssr:!1},r),i}},5853:function(r,n,e){e.d(n,{_T:function(){return u},ev:function(){return o},pi:function(){return t}});var t=function(){return(t=Object.assign||function(r){for(var n,e=1,t=arguments.length;e<t;e++)for(var u in n=arguments[e])Object.prototype.hasOwnProperty.call(n,u)&&(r[u]=n[u]);return r}).apply(this,arguments)};function u(r,n){var e={};for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&0>n.indexOf(t)&&(e[t]=r[t]);if(null!=r&&"function"==typeof Object.getOwnPropertySymbols)for(var u=0,t=Object.getOwnPropertySymbols(r);u<t.length;u++)0>n.indexOf(t[u])&&Object.prototype.propertyIsEnumerable.call(r,t[u])&&(e[t[u]]=r[t[u]]);return e}function o(r,n,e){if(e||2==arguments.length)for(var t,u=0,o=n.length;u<o;u++)!t&&u in n||(t||(t=Array.prototype.slice.call(n,0,u)),t[u]=n[u]);return r.concat(t||Array.prototype.slice.call(n))}"function"==typeof SuppressedError&&SuppressedError}}]);