"use strict";(()=>{var e={};e.id=8788,e.ids=[8788],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},93690:e=>{e.exports=import("graphql-request")},24889:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{originalPathname:()=>h,patchFetch:()=>l,requestAsyncStorage:()=>d,routeModule:()=>u,serverHooks:()=>p,staticGenerationAsyncStorage:()=>f});var o=r(49303),a=r(88716),n=r(60670),i=r(97882),c=e([i]);i=(c.then?(await c)():c)[0];let u=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/auth/me/route",pathname:"/api/auth/me",filename:"route",bundlePath:"app/api/auth/me/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\auth\\me\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:d,staticGenerationAsyncStorage:f,serverHooks:p}=u,h="/api/auth/me/route";function l(){return(0,n.patchFetch)({serverHooks:p,staticGenerationAsyncStorage:f})}s()}catch(e){s(e)}})},71615:(e,t,r)=>{var s=r(88757);r.o(s,"cookies")&&r.d(t,{cookies:function(){return s.cookies}})},33085:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DraftMode",{enumerable:!0,get:function(){return a}});let s=r(45869),o=r(6278);class a{get isEnabled(){return this._provider.isEnabled}enable(){let e=s.staticGenerationAsyncStorage.getStore();return e&&(0,o.trackDynamicDataAccessed)(e,"draftMode().enable()"),this._provider.enable()}disable(){let e=s.staticGenerationAsyncStorage.getStore();return e&&(0,o.trackDynamicDataAccessed)(e,"draftMode().disable()"),this._provider.disable()}constructor(e){this._provider=e}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88757:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cookies:function(){return f},draftMode:function(){return p},headers:function(){return d}});let s=r(68996),o=r(53047),a=r(92044),n=r(72934),i=r(33085),c=r(6278),l=r(45869),u=r(54580);function d(){let e="headers",t=l.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return o.HeadersAdapter.seal(new Headers({}));(0,c.trackDynamicDataAccessed)(t,e)}return(0,u.getExpectedRequestStore)(e).headers}function f(){let e="cookies",t=l.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return s.RequestCookiesAdapter.seal(new a.RequestCookies(new Headers({})));(0,c.trackDynamicDataAccessed)(t,e)}let r=(0,u.getExpectedRequestStore)(e),o=n.actionAsyncStorage.getStore();return(null==o?void 0:o.isAction)||(null==o?void 0:o.isAppRoute)?r.mutableCookies:r.cookies}function p(){let e=(0,u.getExpectedRequestStore)("draftMode");return new i.DraftMode(e.draftMode)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53047:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return a},ReadonlyHeadersError:function(){return o}});let s=r(38238);class o extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new o}}class a extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,o){if("symbol"==typeof r)return s.ReflectAdapter.get(t,r,o);let a=r.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===a);if(void 0!==n)return s.ReflectAdapter.get(t,n,o)},set(t,r,o,a){if("symbol"==typeof r)return s.ReflectAdapter.set(t,r,o,a);let n=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===n);return s.ReflectAdapter.set(t,i??r,o,a)},has(t,r){if("symbol"==typeof r)return s.ReflectAdapter.has(t,r);let o=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0!==a&&s.ReflectAdapter.has(t,a)},deleteProperty(t,r){if("symbol"==typeof r)return s.ReflectAdapter.deleteProperty(t,r);let o=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0===a||s.ReflectAdapter.deleteProperty(t,a)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return o.callable;default:return s.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new a(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,s]of this.entries())e.call(t,s,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},68996:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return d},ReadonlyRequestCookiesError:function(){return n},RequestCookiesAdapter:function(){return i},appendMutableCookies:function(){return u},getModifiedCookieValues:function(){return l}});let s=r(92044),o=r(38238),a=r(45869);class n extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new n}}class i{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return n.callable;default:return o.ReflectAdapter.get(e,t,r)}}})}}let c=Symbol.for("next.mutated.cookies");function l(e){let t=e[c];return t&&Array.isArray(t)&&0!==t.length?t:[]}function u(e,t){let r=l(t);if(0===r.length)return!1;let o=new s.ResponseCookies(e),a=o.getAll();for(let e of r)o.set(e);for(let e of a)o.set(e);return!0}class d{static wrap(e,t){let r=new s.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let n=[],i=new Set,l=()=>{let e=a.staticGenerationAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),n=r.getAll().filter(e=>i.has(e.name)),t){let e=[];for(let t of n){let r=new s.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case c:return n;case"delete":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{l()}};case"set":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{l()}};default:return o.ReflectAdapter.get(e,t,r)}}})}}},97882:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{GET:()=>l,dynamic:()=>u});var o=r(87070),a=r(71615),n=r(93690),i=r(70591),c=e([n]);n=(c.then?(await c)():c)[0];let u="force-dynamic",d=`
  query GetCustomer {
    customer {
      id
      databaseId
      email
      firstName
      lastName
    }
  }
`;async function l(){try{let e=(0,a.cookies)(),t=e.get("woo_auth_token")?.value;if(!t)return o.NextResponse.json({success:!1,message:"Not authenticated"},{status:401});try{let e=(0,i.o)(t),r=Date.now()/1e3;if(e.exp<r)return o.NextResponse.json({success:!1,message:"Token expired"},{status:401})}catch(e){return console.error("Error decoding token:",e),o.NextResponse.json({success:!1,message:"Invalid token"},{status:401})}let r=process.env.WOOCOMMERCE_GRAPHQL_URL||"",s=new n.GraphQLClient(r,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`}}),c=await s.request(d);if(!c.customer)return o.NextResponse.json({success:!1,message:"Invalid user"},{status:401});return o.NextResponse.json({success:!0,user:c.customer,isAuthenticated:!0,token:t})}catch(e){return console.error("Auth me API error:",e),o.NextResponse.json({success:!1,message:"Server error during authentication"},{status:500})}}s()}catch(e){s(e)}})},70591:(e,t,r)=>{r.d(t,{o:()=>o});class s extends Error{}function o(e,t){let r;if("string"!=typeof e)throw new s("Invalid token specified: must be a string");t||(t={});let o=!0===t.header?0:1,a=e.split(".")[o];if("string"!=typeof a)throw new s(`Invalid token specified: missing part #${o+1}`);try{r=function(e){let t=e.replace(/-/g,"+").replace(/_/g,"/");switch(t.length%4){case 0:break;case 2:t+="==";break;case 3:t+="=";break;default:throw Error("base64 string is not of the correct length")}try{var r;return r=t,decodeURIComponent(atob(r).replace(/(.)/g,(e,t)=>{let r=t.charCodeAt(0).toString(16).toUpperCase();return r.length<2&&(r="0"+r),"%"+r}))}catch(e){return atob(t)}}(a)}catch(e){throw new s(`Invalid token specified: invalid base64 for part #${o+1} (${e.message})`)}try{return JSON.parse(r)}catch(e){throw new s(`Invalid token specified: invalid json for part #${o+1} (${e.message})`)}}s.prototype.name="InvalidTokenError"}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,5972],()=>r(24889));module.exports=s})();