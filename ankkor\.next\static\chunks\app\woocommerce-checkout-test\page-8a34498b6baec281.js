(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4335],{92649:function(e,t,s){Promise.resolve().then(s.bind(s,82494))},82494:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return l}});var r=s(57437),o=s(2265),c=s(82372);function l(){let[e,t]=(0,o.useState)(!1),[s,l]=(0,o.useState)("https://maroon-lapwing-781450.hostingersite.com"),[n,a]=(0,o.useState)(""),[i,d]=(0,o.useState)(!1),[m,h]=(0,o.useState)("guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0"),[u,x]=(0,o.useState)("");return(0,r.jsxs)("div",{className:"container mx-auto p-6 max-w-4xl",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"WooCommerce Checkout Test"}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Generate Checkout URL"}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Base URL"}),(0,r.jsx)("input",{type:"text",value:s,onChange:e=>l(e.target.value),className:"w-full p-2 border border-gray-300 rounded",placeholder:"https://your-woocommerce-site.com"})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Cart ID (optional)"}),(0,r.jsx)("input",{type:"text",value:n,onChange:e=>a(e.target.value),className:"w-full p-2 border border-gray-300 rounded",placeholder:"cart123"})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:i,onChange:e=>d(e.target.checked),className:"mr-2"}),(0,r.jsx)("span",{children:"Is User Logged In?"})]}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"If checked, guest checkout parameters will not be added"})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Additional Parameters"}),(0,r.jsx)("input",{type:"text",value:m,onChange:e=>h(e.target.value),className:"w-full p-2 border border-gray-300 rounded",placeholder:"guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0"})]}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsx)("button",{onClick:()=>{try{let e=(0,c.ML)(n,i);x(e)}catch(e){console.error("Error generating URL:",e),alert("Error generating URL. Check console for details.")}},className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Generate Using Library Function"}),(0,r.jsx)("button",{onClick:()=>{try{let e="".concat(s,"/checkout/?").concat(m);x(e)}catch(e){console.error("Error generating direct URL:",e),alert("Error generating direct URL. Check console for details.")}},className:"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700",children:"Generate Direct URL"})]})]}),u&&(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Generated URL"}),(0,r.jsx)("div",{className:"mb-4 p-3 bg-gray-100 rounded overflow-x-auto",children:(0,r.jsx)("code",{className:"text-sm break-all",children:u})}),(0,r.jsx)("button",{onClick:()=>{if(!u){alert("Please generate a URL first");return}t(!0),window.location.href=u},disabled:e,className:"px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 ".concat(e?"opacity-50 cursor-not-allowed":""),children:e?"Redirecting...":"Test This URL"})]}),(0,r.jsxs)("div",{className:"mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded",children:[(0,r.jsx)("h3",{className:"font-semibold text-yellow-800",children:"Troubleshooting Tips"}),(0,r.jsxs)("ul",{className:"list-disc pl-5 mt-2 text-sm",children:[(0,r.jsxs)("li",{children:["If you're redirected to login, check your WooCommerce settings under ",(0,r.jsx)("strong",{children:"WooCommerce > Settings > Accounts & Privacy"})]}),(0,r.jsx)("li",{children:'Ensure "Allow customers to place orders without an account" is checked'}),(0,r.jsx)("li",{children:"Try using an incognito/private browser window to avoid session conflicts"}),(0,r.jsx)("li",{children:"Check browser console for any errors during redirection"})]})]})]})}}},function(e){e.O(0,[5540,4822,8787,8261,1539,2188,6003,7231,2323,9960,7696,8002,7111,8989,4513,8476,8496,8966,3903,6076,4596,62,6271,8726,8133,8049,2870,632,6613,9965,5270,2647,9971,9290,6459,7800,4818,1729,5526,4615,7406,2532,2068,4595,3280,8790,1104,7158,7044,9482,6628,986,4754,1744],function(){return e(e.s=92649)}),_N_E=e.O()}]);