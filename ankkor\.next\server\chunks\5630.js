"use strict";exports.id=5630,exports.ids=[5630],exports.modules={85630:(t,o,e)=>{e.d(o,{calculateShippingCost:()=>a,getLocationFromPincode:()=>i});let i=async t=>{try{let o=await fetch(`https://api.postalpincode.in/pincode/${t}`);if(!o.ok)throw Error("Failed to fetch location data");let e=await o.json();if(!e||0===e.length||"Success"!==e[0].Status)throw Error("Invalid pincode or no data found");let i=e[0].PostOffice[0];return{latitude:0,longitude:0,city:i.District,state:i.State,pincode:t,country:"India"}}catch(t){throw Error("Failed to get location from pincode")}},a=(t,o)=>o>2999?0:t.toLowerCase().includes("punjab")?49:99}};