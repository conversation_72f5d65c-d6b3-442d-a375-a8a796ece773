"use strict";(()=>{var e={};e.id=6091,e.ids=[6091],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},93690:e=>{e.exports=import("graphql-request")},20542:(e,t,r)=>{r.a(e,async(e,o)=>{try{r.r(t),r.d(t,{GlobalError:()=>s.Z,__next_app__:()=>g,originalPathname:()=>f,pages:()=>p,routeModule:()=>m,tree:()=>l});var n=r(15100);r(11360),r(7629),r(11930),r(12523);var a=r(23191),i=r(88716),s=r(43315),d=r(95231),c={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>d[e]);r.d(t,c);var u=e([n]);n=(u.then?(await u)():u)[0];let l=["",{children:["category",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,15100)),"E:\\ankkorwoo\\ankkor\\src\\app\\category\\[slug]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,11360)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,7629)),"E:\\ankkorwoo\\ankkor\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,11930)),"E:\\ankkorwoo\\ankkor\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],p=["E:\\ankkorwoo\\ankkor\\src\\app\\category\\[slug]\\page.tsx"],f="/category/[slug]/page",g={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/category/[slug]/page",pathname:"/category/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}});o()}catch(e){o(e)}})},58585:(e,t,r)=>{var o=r(61085);r.o(o,"notFound")&&r.d(t,{notFound:function(){return o.notFound}}),r.o(o,"redirect")&&r.d(t,{redirect:function(){return o.redirect}})},61085:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return i},RedirectType:function(){return o.RedirectType},notFound:function(){return n.notFound},permanentRedirect:function(){return o.permanentRedirect},redirect:function(){return o.redirect}});let o=r(83953),n=r(16399);class a extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class i extends URLSearchParams{append(){throw new a}delete(){throw new a}set(){throw new a}sort(){throw new a}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return n},notFound:function(){return o}});let r="NEXT_NOT_FOUND";function o(){let e=Error(r);throw e.digest=r,e}function n(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,r)=>{var o;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return o},getRedirectError:function(){return d},getRedirectStatusCodeFromError:function(){return g},getRedirectTypeFromError:function(){return f},getURLFromRedirectError:function(){return p},isRedirectError:function(){return l},permanentRedirect:function(){return u},redirect:function(){return c}});let n=r(54580),a=r(72934),i=r(8586),s="NEXT_REDIRECT";function d(e,t,r){void 0===r&&(r=i.RedirectStatusCode.TemporaryRedirect);let o=Error(s);o.digest=s+";"+t+";"+e+";"+r+";";let a=n.requestAsyncStorage.getStore();return a&&(o.mutableCookies=a.mutableCookies),o}function c(e,t){void 0===t&&(t="replace");let r=a.actionAsyncStorage.getStore();throw d(e,t,(null==r?void 0:r.isAction)?i.RedirectStatusCode.SeeOther:i.RedirectStatusCode.TemporaryRedirect)}function u(e,t){void 0===t&&(t="replace");let r=a.actionAsyncStorage.getStore();throw d(e,t,(null==r?void 0:r.isAction)?i.RedirectStatusCode.SeeOther:i.RedirectStatusCode.PermanentRedirect)}function l(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,o,n]=e.digest.split(";",4),a=Number(n);return t===s&&("replace"===r||"push"===r)&&"string"==typeof o&&!isNaN(a)&&a in i.RedirectStatusCode}function p(e){return l(e)?e.digest.split(";",3)[2]:null}function f(e){if(!l(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function g(e){if(!l(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(o||(o={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},15100:(e,t,r)=>{r.a(e,async(e,o)=>{try{r.r(t),r.d(t,{default:()=>c,generateMetadata:()=>d});var n=r(19510);r(71159);var a=r(58585),i=r(19910),s=e([i]);async function d({params:e}){let{slug:t}=e,r=await (0,i.CP)(),o=r.nodes?.find(e=>e.slug===t);return o?{title:`${o.name} | Ankkor`,description:o.description||`Browse our collection of ${o.name.toLowerCase()} products.`}:{title:"Category Not Found | Ankkor",description:"The requested category could not be found."}}async function c({params:e}){let{slug:t}=e,r=await (0,i.CP)(),o=r.nodes?.find(e=>e.slug===t);o||(0,a.notFound)();let s=(await (0,i.Xp)({first:12,where:{categoryIn:[o.slug]}})).nodes||[];return(0,n.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,n.jsxs)("header",{className:"mb-8 text-center",children:[n.jsx("h1",{className:"text-3xl font-serif text-[#2c2c27] mb-2",children:o.name}),o.description&&n.jsx("p",{className:"text-[#8a8778] max-w-2xl mx-auto",children:o.description}),(0,n.jsxs)("p",{className:"text-[#5c5c52] mt-2",children:[o.count," products"]})]}),0===s.length?n.jsx("div",{className:"text-center py-12",children:n.jsx("p",{className:"text-gray-500",children:"No products found in this category."})}):n.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6",children:s.map(e=>n.jsx("div",{className:"product-card",children:(0,n.jsxs)("a",{href:`/product/${e.slug}`,className:"block",children:[e.image&&n.jsx("div",{className:"relative aspect-square bg-[#f4f3f0] overflow-hidden mb-4",children:n.jsx("img",{src:e.image.sourceUrl,alt:e.name,className:"w-full h-full object-cover"})}),n.jsx("h3",{className:"text-lg font-medium",children:e.name}),(0,n.jsxs)("p",{className:"text-gray-600",children:["$",parseFloat(e.price).toFixed(2)]})]})},e.id))})]})}i=(s.then?(await s)():s)[0],o()}catch(e){o(e)}})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,1057,4766,4868,5436,9910],()=>r(20542));module.exports=o})();