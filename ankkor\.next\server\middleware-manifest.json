{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/status\\/redis(.json)?[\\/#\\?]?$", "originalSource": "/api/status/redis"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/account(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/account/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/checkout(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/checkout/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-in(.json)?[\\/#\\?]?$", "originalSource": "/sign-in"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-up(.json)?[\\/#\\?]?$", "originalSource": "/sign-up"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "gFwKYr1g-9QX9WyR0PFI2", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vwVv4cdJHcLKKQruBZKV6TYb+qDFHuxLXCDl8Z8pqzE=", "__NEXT_PREVIEW_MODE_ID": "77186eb6961c655a06b469e050650d09", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "3a0fc8cfdfd14762ab1d1b08f5a39a83b3e3a533e75d6d806a49e59330db29cd", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e0cca5c4c8dea0baa702d5360fb152e46eb8f8668e7e8a57a429ca38fcd0d07c"}}}, "functions": {}, "sortedMiddleware": ["/"]}