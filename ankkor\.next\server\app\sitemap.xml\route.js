"use strict";(()=>{var e={};e.id=6717,e.ids=[6717],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},97189:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>x,patchFetch:()=>M,requestAsyncStorage:()=>g,routeModule:()=>f,serverHooks:()=>F,staticGenerationAsyncStorage:()=>q});var i={};r.r(i),r.d(i,{default:()=>c});var o={};r.r(o),r.d(o,{GET:()=>h});var n=r(49303),a=r(88716),l=r(60670),s=r(55661);let u="https://ankkor.in";function c(){let e=new Date;return[{url:`${u}`,lastModified:e,changeFrequency:"weekly",priority:1},{url:`${u}/about`,lastModified:e,changeFrequency:"monthly",priority:.8},{url:`${u}/customer-service`,lastModified:e,changeFrequency:"monthly",priority:.7},{url:`${u}/customer-service/contact`,lastModified:e,changeFrequency:"monthly",priority:.7},{url:`${u}/customer-service/faq`,lastModified:e,changeFrequency:"monthly",priority:.7},{url:`${u}/customer-service/size-guide`,lastModified:e,changeFrequency:"monthly",priority:.7},{url:`${u}/collection`,lastModified:e,changeFrequency:"weekly",priority:.9},{url:`${u}/collection/shirts`,lastModified:e,changeFrequency:"weekly",priority:.9},{url:`${u}/collection/polos`,lastModified:e,changeFrequency:"weekly",priority:.9},{url:`${u}/collection/pants`,lastModified:e,changeFrequency:"weekly",priority:.9},{url:`${u}/shipping-policy`,lastModified:e,changeFrequency:"monthly",priority:.6},{url:`${u}/return-policy`,lastModified:e,changeFrequency:"monthly",priority:.6},{url:`${u}/privacy-policy`,lastModified:e,changeFrequency:"monthly",priority:.5},{url:`${u}/terms-of-service`,lastModified:e,changeFrequency:"monthly",priority:.5},{url:`${u}/sign-in`,lastModified:e,changeFrequency:"monthly",priority:.5},{url:`${u}/sign-up`,lastModified:e,changeFrequency:"monthly",priority:.5},{url:`${u}/wishlist`,lastModified:e,changeFrequency:"monthly",priority:.6},{url:`${u}/cart`,lastModified:e,changeFrequency:"monthly",priority:.6}]}var p=r(60707);let d={...i},y=d.default,m=d.generateSitemaps;if("function"!=typeof y)throw Error('Default export is missing in "E:\\ankkorwoo\\ankkor\\src\\app\\sitemap.ts"');async function h(e,t){let r;let{__metadata_id__:i,...o}=t.params||{},n=m?await m():null;if(n&&null==(r=n.find(e=>{let t=e.id.toString();return(t+=".xml")===i})?.id))return new s.NextResponse("Not Found",{status:404});let a=await y({id:r}),l=(0,p.resolveRouteData)(a,"sitemap");return new s.NextResponse(l,{headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=0, must-revalidate"}})}let f=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/sitemap.xml/route",pathname:"/sitemap.xml",filename:"sitemap",bundlePath:"app/sitemap.xml/route"},resolvedPagePath:"next-metadata-route-loader?page=%2Fsitemap.xml%2Froute&filePath=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp%5Csitemap.ts&isDynamic=1!?__next_metadata_route__",nextConfigOutput:"",userland:o}),{requestAsyncStorage:g,staticGenerationAsyncStorage:q,serverHooks:F}=f,x="/sitemap.xml/route";function M(){return(0,l.patchFetch)({serverHooks:F,staticGenerationAsyncStorage:q})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[8948,1346],()=>r(97189));module.exports=i})();