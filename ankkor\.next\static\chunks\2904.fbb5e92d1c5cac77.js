"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2904],{62904:function(e,t,n){n.d(t,{Z:function(){return F}});var r=n(5853),o=n(2265),a="right-scroll-bar-position",c="width-before-scroll-bar",i=n(17325),u=(0,n(31412)._)(),l=function(){},d=o.forwardRef(function(e,t){var n=o.useRef(null),a=o.useState({onScrollCapture:l,onWheelCapture:l,onTouchMoveCapture:l}),c=a[0],d=a[1],s=e.forwardProps,f=e.children,h=e.className,v=e.removeScrollBar,p=e.enabled,m=e.shards,g=e.sideCar,w=e.noIsolation,b=e.inert,y=e.allowPinchZoom,E=e.as,C=e.gapMode,k=(0,r._T)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=(0,i.q)([n,t]),S=(0,r.pi)((0,r.pi)({},k),c);return o.createElement(o.Fragment,null,p&&o.createElement(g,{sideCar:u,removeScrollBar:v,shards:m,noIsolation:w,inert:b,setCallbacks:d,allowPinchZoom:!!y,lockRef:n,gapMode:C}),s?o.cloneElement(o.Children.only(f),(0,r.pi)((0,r.pi)({},S),{ref:R})):o.createElement(void 0===E?"div":E,(0,r.pi)({},S,{className:h,ref:R}),f))});d.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},d.classNames={fullWidth:c,zeroRight:a};var s=n(49085),f=n(18403),h=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=(0,f.V)();return t&&e.setAttribute("nonce",t),e}())){var r,o;(r=t).styleSheet?r.styleSheet.cssText=n:r.appendChild(document.createTextNode(n)),o=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(o)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},v=function(){var e=h();return function(t,n){o.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},p=function(){var e=v();return function(t){return e(t.styles,t.dynamic),null}},m={left:0,top:0,right:0,gap:0},g=function(e){return parseInt(e||"",10)||0},w=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[g(n),g(r),g(o)]},b=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return m;var t=w(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},y=p(),E="data-scroll-locked",C=function(e,t,n,r){var o=e.left,i=e.top,u=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(E,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(u,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(a," .").concat(a," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(E,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},k=function(){var e=parseInt(document.body.getAttribute(E)||"0",10);return isFinite(e)?e:0},R=function(){o.useEffect(function(){return document.body.setAttribute(E,(k()+1).toString()),function(){var e=k()-1;e<=0?document.body.removeAttribute(E):document.body.setAttribute(E,e.toString())}},[])},S=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,a=void 0===r?"margin":r;R();var c=o.useMemo(function(){return b(a)},[a]);return o.createElement(y,{styles:C(c,!t,a,n?"":"!important")})},L=!1;if("undefined"!=typeof window)try{var N=Object.defineProperty({},"passive",{get:function(){return L=!0,!0}});window.addEventListener("test",N,N),window.removeEventListener("test",N,N)}catch(e){L=!1}var T=!!L&&{passive:!1},M=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},x=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),P(e,r)){var o=B(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},P=function(e,t){return"v"===e?M(t,"overflowY"):M(t,"overflowX")},B=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},A=function(e,t,n,r,o){var a,c=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),i=c*r,u=n.target,l=t.contains(u),d=!1,s=i>0,f=0,h=0;do{var v=B(e,u),p=v[0],m=v[1]-v[2]-c*p;(p||m)&&P(e,u)&&(f+=m,h+=p),u instanceof ShadowRoot?u=u.host:u=u.parentNode}while(!l&&u!==document.body||l&&(t.contains(u)||t===u));return s&&(o&&1>Math.abs(f)||!o&&i>f)?d=!0:!s&&(o&&1>Math.abs(h)||!o&&-i>h)&&(d=!0),d},I=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},W=function(e){return[e.deltaX,e.deltaY]},X=function(e){return e&&"current"in e?e.current:e},Y=0,_=[],Z=(0,s.L)(u,function(e){var t=o.useRef([]),n=o.useRef([0,0]),a=o.useRef(),c=o.useState(Y++)[0],i=o.useState(p)[0],u=o.useRef(e);o.useEffect(function(){u.current=e},[e]),o.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(c));var t=(0,r.ev)([e.lockRef.current],(e.shards||[]).map(X),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(c))}),function(){document.body.classList.remove("block-interactivity-".concat(c)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(c))})}}},[e.inert,e.lockRef.current,e.shards]);var l=o.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!u.current.allowPinchZoom;var r,o=I(e),c=n.current,i="deltaX"in e?e.deltaX:c[0]-o[0],l="deltaY"in e?e.deltaY:c[1]-o[1],d=e.target,s=Math.abs(i)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===s&&"range"===d.type)return!1;var f=x(s,d);if(!f)return!0;if(f?r=s:(r="v"===s?"h":"v",f=x(s,d)),!f)return!1;if(!a.current&&"changedTouches"in e&&(i||l)&&(a.current=r),!r)return!0;var h=a.current||r;return A(h,t,e,"h"===h?i:l,!0)},[]),d=o.useCallback(function(e){if(_.length&&_[_.length-1]===i){var n="deltaY"in e?W(e):I(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(u.current.shards||[]).map(X).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!u.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=o.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),f=o.useCallback(function(e){n.current=I(e),a.current=void 0},[]),h=o.useCallback(function(t){s(t.type,W(t),t.target,l(t,e.lockRef.current))},[]),v=o.useCallback(function(t){s(t.type,I(t),t.target,l(t,e.lockRef.current))},[]);o.useEffect(function(){return _.push(i),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:v}),document.addEventListener("wheel",d,T),document.addEventListener("touchmove",d,T),document.addEventListener("touchstart",f,T),function(){_=_.filter(function(e){return e!==i}),document.removeEventListener("wheel",d,T),document.removeEventListener("touchmove",d,T),document.removeEventListener("touchstart",f,T)}},[]);var m=e.removeScrollBar,g=e.inert;return o.createElement(o.Fragment,null,g?o.createElement(i,{styles:"\n  .block-interactivity-".concat(c," {pointer-events: none;}\n  .allow-interactivity-").concat(c," {pointer-events: all;}\n")}):null,m?o.createElement(S,{gapMode:e.gapMode}):null)}),D=o.forwardRef(function(e,t){return o.createElement(d,(0,r.pi)({},e,{ref:t,sideCar:Z}))});D.classNames=d.classNames;var F=D}}]);