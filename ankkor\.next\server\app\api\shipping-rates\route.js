"use strict";(()=>{var e={};e.id=7546,e.ids=[7546],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},90935:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>y,patchFetch:()=>f,requestAsyncStorage:()=>h,routeModule:()=>l,serverHooks:()=>g,staticGenerationAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{POST:()=>p});var i=r(49303),a=r(88716),n=r(60670),o=r(87070);async function p(e){try{let{pincode:t,cartItems:r,state:s}=await e.json();if(!t||!r||!Array.isArray(r))return o.NextResponse.json({error:"Invalid request data"},{status:400});if(!/^[0-9]{6}$/.test(t))return o.NextResponse.json({error:"Invalid pincode format"},{status:400});let i=process.env.SHIPPING_PROVIDER||"woocommerce",a=[];return a="woocommerce"===i?await d(t,r,s):"delhivery"===i?await c(t,r,s):await u(t,r,s),o.NextResponse.json(a)}catch(e){return console.error("Shipping rates error:",e),o.NextResponse.json({error:e.message||"Failed to calculate shipping rates"},{status:500})}}async function d(e,t,r){try{let s="https://maroon-lapwing-781450.hostingersite.com",i=process.env.WOOCOMMERCE_CONSUMER_KEY,a=process.env.WOOCOMMERCE_CONSUMER_SECRET;if(!s||!i||!a)throw Error("WooCommerce credentials not configured");let n=t.reduce((e,t)=>{let r="string"==typeof t.price?parseFloat(t.price):t.price;return e+r*t.quantity},0);t.reduce((e,t)=>e+(t.weight||.5)*t.quantity,0);let o=Buffer.from(`${i}:${a}`).toString("base64"),p=await fetch(`${s}/wp-json/wc/v3/shipping/zones`,{headers:{Authorization:`Basic ${o}`}});if(!p.ok)throw Error("Failed to fetch shipping zones");let d=await p.json(),c=[];for(let e of d){if(0===e.id)continue;let t=await fetch(`${s}/wp-json/wc/v3/shipping/zones/${e.id}/methods`,{headers:{Authorization:`Basic ${o}`}});if(t.ok){for(let r of(await t.json()))if(r.enabled){let t=0;if("flat_rate"===r.method_id)t=parseFloat(r.settings?.cost?.value||"0");else if("free_shipping"===r.method_id){let e=parseFloat(r.settings?.min_amount?.value||"0");t=n>=e?0:parseFloat(r.settings?.cost?.value||"50")}else"local_pickup"===r.method_id&&(t=parseFloat(r.settings?.cost?.value||"0"));c.push({id:`${e.id}_${r.instance_id}`,name:0===t?"Free Shipping":"Standard Shipping",cost:t,description:0===t?"Free shipping on orders above minimum amount":"Standard delivery across India",estimatedDays:"5-7 days"});break}if(c.length>0)break}}if(0===c.length)return u(e,t,r);return c}catch(s){return console.error("WooCommerce shipping error:",s),u(e,t,r)}}async function c(e,t,r){try{let e=t.reduce((e,t)=>e+(t.weight||.5)*t.quantity,0);return[{id:"delhivery_standard",name:"Standard Shipping",cost:Math.max(50,10*e),description:"Standard delivery across India",estimatedDays:"5-7 days"}]}catch(r){return console.error("Delhivery shipping error:",r),u(e,t)}}async function u(e,t,s){let{calculateShippingCost:i,getLocationFromPincode:a}=await r.e(5630).then(r.bind(r,85630)),n=t.reduce((e,t)=>e+("string"==typeof t.price?parseFloat(t.price):t.price)*t.quantity,0),p=s||"",d=99;if(!s)return o.NextResponse.json({error:"Please select a state to calculate shipping"},{status:400});d=i(p=s,n);let c=[];return c.push({id:"standard",name:"Standard Shipping",cost:d,description:"Standard delivery across India",estimatedDays:"5-7 days",state:p}),["110001","400001","560001","600001","700001"].includes(e)?(c.push({id:"express",name:"Express Shipping",cost:150,description:"Delivered in 2-3 business days",estimatedDays:"2-3 days"}),c.push({id:"same_day",name:"Same Day Delivery",cost:300,description:"Delivered today before 9 PM",estimatedDays:"Today"})):c.push({id:"express",name:"Express Shipping",cost:200,description:"Delivered in 3-4 business days",estimatedDays:"3-4 days"}),c}let l=new i.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/shipping-rates/route",pathname:"/api/shipping-rates",filename:"route",bundlePath:"app/api/shipping-rates/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\shipping-rates\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:h,staticGenerationAsyncStorage:m,serverHooks:g}=l,y="/api/shipping-rates/route";function f(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:m})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,5972],()=>r(90935));module.exports=s})();