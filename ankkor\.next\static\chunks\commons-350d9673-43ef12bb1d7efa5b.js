"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4754],{82372:function(n,t,e){e.d(t,{Bk:function(){return nt},Id:function(){return ns},ML:function(){return nu},Op:function(){return na},Xp:function(){return W},Xq:function(){return no},dv:function(){return ne},getAllCategories:function(){return Z},getAllProducts:function(){return Y},getCategoryProducts:function(){return np},gk:function(){return nc},h2:function(){return nr},mJ:function(){return ni},s3:function(){return nd},testWooCommerceConnection:function(){return nn},xu:function(){return ng}});var o=e(45008),r=e(34206),a=e(87466),i=e(40257);function s(){let n=(0,o._)(["\n  fragment ProductFields on Product {\n    id\n    databaseId\n    name\n    slug\n    description\n    shortDescription\n    type\n    image {\n      sourceUrl\n      altText\n    }\n    galleryImages {\n      nodes {\n        sourceUrl\n        altText\n      }\n    }\n    ... on SimpleProduct {\n      price\n      regularPrice\n      salePrice\n      onSale\n      stockStatus\n      stockQuantity\n    }\n    ... on VariableProduct {\n      price\n      regularPrice\n      salePrice\n      onSale\n      stockStatus\n      stockQuantity\n      attributes {\n        nodes {\n          name\n          options\n        }\n      }\n    }\n  }\n"]);return s=function(){return n},n}function u(){let n=(0,o._)(["\n  fragment VariableProductWithVariations on VariableProduct {\n    attributes {\n      nodes {\n        name\n        options\n      }\n    }\n    variations {\n      nodes {\n        id\n        databaseId\n        name\n        price\n        regularPrice\n        salePrice\n        stockStatus\n        stockQuantity\n        attributes {\n          nodes {\n            name\n            value\n          }\n        }\n      }\n    }\n  }\n"]);return u=function(){return n},n}function c(){let n=(0,o._)(["\n  query GetProducts(\n    $first: Int\n    $after: String\n    $where: RootQueryToProductConnectionWhereArgs\n  ) {\n    products(first: $first, after: $after, where: $where) {\n      pageInfo {\n        hasNextPage\n        endCursor\n      }\n      nodes {\n        ...ProductFields\n        ... on VariableProduct {\n          ...VariableProductWithVariations\n        }\n      }\n    }\n  }\n  ","\n  ","\n"]);return c=function(){return n},n}function l(){let n=(0,o._)(["\n  query GetProductBySlug($slug: ID!) {\n    product(id: $slug, idType: SLUG) {\n      ...ProductFields\n      ... on VariableProduct {\n        ...VariableProductWithVariations\n      }\n    }\n  }\n  ","\n  ","\n"]);return l=function(){return n},n}function d(){let n=(0,o._)(["\n  query GetProductBySlugWithTags($slug: ID!) {\n    product(id: $slug, idType: SLUG) {\n      ...ProductFields\n      ... on VariableProduct {\n        ...VariableProductWithVariations\n      }\n      productTags {\n        nodes {\n          id\n          name\n          slug\n        }\n      }\n      productCategories {\n        nodes {\n          id\n          name\n          slug\n        }\n      }\n    }\n  }\n  ","\n  ","\n"]);return d=function(){return n},n}function p(){let n=(0,o._)(["\n  query GetCategories(\n    $first: Int\n    $after: String\n    $where: RootQueryToProductCategoryConnectionWhereArgs\n  ) {\n    productCategories(first: $first, after: $after, where: $where) {\n      pageInfo {\n        hasNextPage\n        endCursor\n      }\n      nodes {\n        id\n        databaseId\n        name\n        slug\n        description\n        count\n        image {\n          sourceUrl\n          altText\n        }\n      }\n    }\n  }\n"]);return p=function(){return n},n}function m(){let n=(0,o._)(["\n  query GetAllProducts($first: Int = 20) {\n    products(first: $first) {\n      nodes {\n        id\n        databaseId\n        name\n        slug\n        description\n        shortDescription\n        productCategories {\n          nodes {\n            id\n            name\n            slug\n          }\n        }\n        ... on SimpleProduct {\n          price\n          regularPrice\n          salePrice\n          onSale\n          stockStatus\n          stockQuantity\n        }\n        ... on VariableProduct {\n          price\n          regularPrice\n          salePrice\n          onSale\n          stockStatus\n          variations {\n            nodes {\n              stockStatus\n              stockQuantity\n            }\n          }\n        }\n        image {\n          id\n          sourceUrl\n          altText\n        }\n        galleryImages {\n          nodes {\n            id\n            sourceUrl\n            altText\n          }\n        }\n        ... on VariableProduct {\n          attributes {\n            nodes {\n              name\n              options\n            }\n          }\n        }\n        ... on SimpleProduct {\n          attributes {\n            nodes {\n              name\n              options\n            }\n          }\n        }\n      }\n    }\n  }\n"]);return m=function(){return n},n}function g(){let n=(0,o._)(["\n  query GetProductsByCategory($slug: ID!, $first: Int = 20) {\n    productCategory(id: $slug, idType: SLUG) {\n      id\n      name\n      slug\n      description\n      products(first: $first) {\n        nodes {\n          id\n          databaseId\n          name\n          slug\n          description\n          shortDescription\n          productCategories {\n            nodes {\n              id\n              name\n              slug\n            }\n          }\n          ... on SimpleProduct {\n            price\n            regularPrice\n            salePrice\n            onSale\n            stockStatus\n            stockQuantity\n          }\n          ... on VariableProduct {\n            price\n            regularPrice\n            salePrice\n            onSale\n            stockStatus\n            variations {\n              nodes {\n                stockStatus\n                stockQuantity\n              }\n            }\n          }\n          image {\n            id\n            sourceUrl\n            altText\n          }\n          galleryImages {\n            nodes {\n              id\n              sourceUrl\n              altText\n            }\n          }\n          ... on VariableProduct {\n            attributes {\n              nodes {\n                name\n                options\n              }\n            }\n          }\n          ... on SimpleProduct {\n            attributes {\n              nodes {\n                name\n                options\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n"]);return g=function(){return n},n}function f(){let n=(0,o._)(["\n  query GetAllCategories($first: Int = 20) {\n    productCategories(first: $first) {\n      nodes {\n        id\n        databaseId\n        name\n        slug\n        description\n        count\n        image {\n          sourceUrl\n          altText\n        }\n        children {\n          nodes {\n            id\n            name\n            slug\n          }\n        }\n      }\n    }\n  }\n"]);return f=function(){return n},n}function y(){let n=(0,o._)(["\n  query GetCart {\n    cart {\n      contents {\n        nodes {\n          key\n          product {\n            node {\n              id\n              databaseId\n              name\n              slug\n              type\n              image {\n                sourceUrl\n                altText\n              }\n            }\n          }\n          variation {\n            node {\n              id\n              databaseId\n              name\n              attributes {\n                nodes {\n                  name\n                  value\n                }\n              }\n            }\n          }\n          quantity\n          total\n        }\n      }\n      subtotal\n      total\n      totalTax\n      isEmpty\n    }\n  }\n"]);return y=function(){return n},n}function h(){let n=(0,o._)(['\n  mutation LoginUser($username: String!, $password: String!) {\n    login(input: {\n      clientMutationId: "login"\n      username: $username\n      password: $password\n    }) {\n      authToken\n      refreshToken\n      user {\n        id\n        databaseId\n        email\n        firstName\n        lastName\n        nicename\n        nickname\n        username\n      }\n    }\n  }\n']);return h=function(){return n},n}function v(){let n=(0,o._)(["\n  query GetCart {\n    cart {\n      contents {\n        nodes {\n          key\n          product {\n            node {\n              id\n              databaseId\n              name\n              slug\n              type\n              image {\n                sourceUrl\n                altText\n              }\n            }\n          }\n          variation {\n            node {\n              id\n              databaseId\n              name\n              attributes {\n                nodes {\n                  name\n                  value\n                }\n              }\n            }\n          }\n          quantity\n          total\n        }\n      }\n      subtotal\n      total\n      totalTax\n      isEmpty\n      contentsCount\n    }\n  }\n"]);return v=function(){return n},n}function P(){let n=(0,o._)(['\n  mutation AddToCart($productId: Int!, $variationId: Int, $quantity: Int, $extraData: String) {\n    addToCart(\n      input: {\n        clientMutationId: "addToCart"\n        productId: $productId\n        variationId: $variationId\n        quantity: $quantity\n        extraData: $extraData\n      }\n    ) {\n      cart {\n        contents {\n          nodes {\n            key\n            product {\n              node {\n                id\n                databaseId\n                name\n                slug\n                type\n                image {\n                  sourceUrl\n                  altText\n                }\n              }\n            }\n            variation {\n              node {\n                id\n                databaseId\n                name\n                attributes {\n                  nodes {\n                    name\n                    value\n                  }\n                }\n              }\n            }\n            quantity\n            total\n          }\n        }\n        subtotal\n        total\n        totalTax\n        isEmpty\n        contentsCount\n      }\n    }\n  }\n']);return P=function(){return n},n}function C(){let n=(0,o._)(["\n  mutation RemoveItemsFromCart($keys: [ID]!, $all: Boolean) {\n    removeItemsFromCart(input: { keys: $keys, all: $all }) {\n      cart {\n        contents {\n          nodes {\n            key\n            product {\n              node {\n                id\n                databaseId\n                name\n                slug\n                type\n                image {\n                  sourceUrl\n                  altText\n                }\n              }\n            }\n            variation {\n              node {\n                id\n                databaseId\n                name\n                attributes {\n                  nodes {\n                    name\n                    value\n                  }\n                }\n              }\n            }\n            quantity\n            total\n          }\n        }\n        subtotal\n        total\n        totalTax\n        isEmpty\n      }\n    }\n  }\n"]);return C=function(){return n},n}function I(){let n=(0,o._)(["\n  query GetShippingMethods {\n    shippingMethods {\n      nodes {\n        id\n        title\n        description\n        cost\n      }\n    }\n  }\n"]);return I=function(){return n},n}function b(){let n=(0,o._)(["\n  query GetPaymentGateways {\n    paymentGateways {\n      nodes {\n        id\n        title\n        description\n        enabled\n      }\n    }\n  }\n"]);return b=function(){return n},n}function w(){let n=(0,o._)(["\n      query TestConnection {\n        generalSettings {\n          title\n          url\n        }\n      }\n    "]);return w=function(){return n},n}function D(){let n=(0,o._)(["\n      query GetProductById($id: ID!) {\n        product(id: $id, idType: DATABASE_ID) {\n          id\n          databaseId\n          name\n          slug\n          description\n          shortDescription\n          productCategories {\n            nodes {\n              id\n              name\n              slug\n            }\n          }\n          ... on SimpleProduct {\n            price\n            regularPrice\n            salePrice\n            onSale\n            stockStatus\n            stockQuantity\n          }\n          ... on VariableProduct {\n            price\n            regularPrice\n            salePrice\n            onSale\n            stockStatus\n            variations {\n              nodes {\n                stockStatus\n                stockQuantity\n              }\n            }\n          }\n          image {\n            id\n            sourceUrl\n            altText\n          }\n        }\n      }\n    "]);return D=function(){return n},n}function S(){let n=(0,o._)(["\n    query SearchProducts($query: String!, $first: Int) {\n      products(first: $first, where: { search: $query }) {\n        nodes {\n          id\n          databaseId\n          name\n          slug\n          price\n          image {\n            sourceUrl\n            altText\n          }\n          shortDescription\n        }\n        pageInfo {\n          hasNextPage\n          endCursor\n        }\n      }\n    }\n  "]);return S=function(){return n},n}function k(){let n=(0,o._)(["\n  mutation CreateCustomer($input: RegisterCustomerInput!) {\n    registerCustomer(input: $input) {\n      customer {\n        id\n        databaseId\n        email\n        firstName\n        lastName\n        displayName\n      }\n      authToken\n      refreshToken\n    }\n  }\n"]);return k=function(){return n},n}function T(){let n=(0,o._)(["\n  mutation UpdateCustomer($input: UpdateCustomerInput!) {\n    updateCustomer(input: $input) {\n      clientMutationId\n      customer {\n        id\n        databaseId\n        email\n        firstName\n        lastName\n        displayName\n        billing {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n          email\n          phone\n        }\n        shipping {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n        }\n      }\n      customerUserErrors {\n        field\n        message\n      }\n    }\n  }\n"]);return T=function(){return n},n}function N(){let n=(0,o._)(["\n  query GetCustomer {\n    customer {\n      id\n      databaseId\n      email\n      firstName\n      lastName\n      displayName\n      username\n      role\n      date\n      modified\n      isPayingCustomer\n      orderCount\n      totalSpent\n      billing {\n        firstName\n        lastName\n        company\n        address1\n        address2\n        city\n        state\n        postcode\n        country\n        email\n        phone\n      }\n      shipping {\n        firstName\n        lastName\n        company\n        address1\n        address2\n        city\n        state\n        postcode\n        country\n      }\n      orders(first: 50) {\n        nodes {\n          id\n          databaseId\n          date\n          status\n          total\n          subtotal\n          totalTax\n          shippingTotal\n          discountTotal\n          paymentMethodTitle\n          customerNote\n          billing {\n            firstName\n            lastName\n            company\n            address1\n            address2\n            city\n            state\n            postcode\n            country\n            email\n            phone\n          }\n          shipping {\n            firstName\n            lastName\n            company\n            address1\n            address2\n            city\n            state\n            postcode\n            country\n          }\n          lineItems {\n            nodes {\n              product {\n                node {\n                  id\n                  name\n                  slug\n                  image {\n                    sourceUrl\n                    altText\n                  }\n                }\n              }\n              variation {\n                node {\n                  id\n                  name\n                  attributes {\n                    nodes {\n                      name\n                      value\n                    }\n                  }\n                }\n              }\n              quantity\n              total\n              subtotal\n              totalTax\n            }\n          }\n          shippingLines {\n            nodes {\n              methodTitle\n              total\n            }\n          }\n          feeLines {\n            nodes {\n              name\n              total\n            }\n          }\n          couponLines {\n            nodes {\n              code\n              discount\n            }\n          }\n        }\n      }\n      downloadableItems {\n        nodes {\n          name\n          downloadId\n          downloadsRemaining\n          accessExpires\n          product {\n            node {\n              id\n              name\n            }\n          }\n        }\n      }\n      metaData {\n        key\n        value\n      }\n    }\n  }\n"]);return N=function(){return n},n}function $(){let n=(0,o._)(["\n  mutation CreateAddress($input: UpdateCustomerInput!) {\n    updateCustomer(input: $input) {\n      customer {\n        id\n        billing {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n          email\n          phone\n        }\n        shipping {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n        }\n      }\n    }\n  }\n"]);return $=function(){return n},n}function _(){let n=(0,o._)(["\n  mutation UpdateAddress($input: UpdateCustomerInput!) {\n    updateCustomer(input: $input) {\n      customer {\n        id\n        billing {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n          email\n          phone\n        }\n        shipping {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n        }\n      }\n    }\n  }\n"]);return _=function(){return n},n}function E(){let n=(0,o._)(["\n  mutation DeleteAddress($input: UpdateCustomerInput!) {\n    updateCustomer(input: $input) {\n      customer {\n        id\n        billing {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n          email\n          phone\n        }\n        shipping {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n        }\n      }\n    }\n  }\n"]);return E=function(){return n},n}function U(){let n=(0,o._)(["\n  mutation SetDefaultAddress($input: UpdateCustomerInput!) {\n    updateCustomer(input: $input) {\n      customer {\n        id\n        billing {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n          email\n          phone\n        }\n        shipping {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n        }\n      }\n    }\n  }\n"]);return U=function(){return n},n}function q(){let n=(0,o._)(["\n  mutation UpdateCart($input: UpdateItemQuantitiesInput!) {\n    updateItemQuantities(input: $input) {\n      cart {\n        contents {\n          nodes {\n            key\n            product {\n              node {\n                id\n                name\n                price\n              }\n            }\n            quantity\n            total\n          }\n        }\n        subtotal\n        total\n        totalTax\n        isEmpty\n      }\n    }\n  }\n"]);return q=function(){return n},n}let x={storeUrl:"https://maroon-lapwing-781450.hostingersite.com",graphqlUrl:i.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql",apiVersion:"v1"},A=null,O=()=>sessionStorage.getItem("woo-session-token")||A,G=n=>{A=n,n?sessionStorage.setItem("woo-session-token",n):sessionStorage.removeItem("woo-session-token")},R=i.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql",Q=new r.g6(R,{headers:{"Content-Type":"application/json",Accept:"application/json"}}),L=(0,r.Ps)(s()),V=(0,r.Ps)(u()),M=(0,r.Ps)(c(),L,V);async function W(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{return(await F(M,{first:n.first||12,after:n.after||null,where:n.where||{}},["products"],60)).products}catch(n){return console.error("Error fetching products:",n),{nodes:[],pageInfo:{hasNextPage:!1,endCursor:null}}}}async function F(n){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};arguments.length>2&&void 0!==arguments[2]&&arguments[2],arguments.length>3&&void 0!==arguments[3]&&arguments[3];try{{let e={"Content-Type":"application/json"},o=O();o&&(e["woocommerce-session"]="Session ".concat(o));let r={method:"POST",headers:e,body:JSON.stringify({query:n,variables:t})},a=await fetch("/api/graphql",r);if(!a.ok)throw Error("GraphQL API responded with status ".concat(a.status));let i=a.headers.get("woocommerce-session");if(i){let n=i.replace("Session ","");G(n)}let{data:s,errors:u}=await a.json();if(u)throw console.error("GraphQL Errors:",u),Error(u[0].message);return s}}catch(n){throw console.error("Error fetching from WooCommerce:",n),n}}async function B(n){let{query:t,variables:e}=n,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1e3,a=0,i=null;for(;a<o;)try{return await F(t,e,[],0)}catch(n){i=n,++a<o&&(console.log("Retrying request (".concat(a,"/").concat(o,") after ").concat(r,"ms")),await new Promise(n=>setTimeout(n,r)),r*=2)}throw console.error("Failed after ".concat(o," attempts:"),i),i}(0,r.Ps)(l(),L,V),(0,r.Ps)(d(),L,V),(0,r.Ps)(p());let H=(0,r.Ps)(m()),j=(0,r.Ps)(g()),J=(0,r.Ps)(f());(0,r.Ps)(y()),(0,r.Ps)(h());let K=(0,r.Ps)(v()),X=(0,r.Ps)(P()),z=(0,r.Ps)(C());async function Y(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:20;try{var t;let e=await B({query:H,variables:{first:n}});return(null==e?void 0:null===(t=e.products)||void 0===t?void 0:t.nodes)||[]}catch(n){return console.error("Error fetching all products:",n),[]}}async function Z(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:20;try{var t;console.log("\uD83D\uDD0D Fetching all categories with first: ".concat(n)),console.log("\uD83D\uDCE1 Using GraphQL endpoint: ".concat(i.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql"));let e=await B({query:J,variables:{first:n}});console.log("\uD83D\uDCCA Raw categories response:",JSON.stringify(e,null,2));let o=(null==e?void 0:null===(t=e.productCategories)||void 0===t?void 0:t.nodes)||[];return console.log("\uD83D\uDCC2 Found ".concat(o.length," categories:"),o.map(n=>({name:n.name,slug:n.slug,id:n.id,databaseId:n.databaseId,count:n.count}))),o}catch(n){return console.error("❌ Error fetching all categories:",n),n instanceof Error&&(console.error("Error message: ".concat(n.message)),console.error("Error stack: ".concat(n.stack))),[]}}async function nn(){try{console.log("\uD83E\uDDEA Testing WooCommerce GraphQL connection..."),console.log("\uD83D\uDCE1 Endpoint: ".concat(i.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql"));let n=(0,r.Ps)(w()),t=await B({query:n,variables:{}});console.log("✅ Basic connection test result:",t);let e=await Z(50);console.log("\uD83D\uDCC2 Available categories (".concat(e.length,"):"),e);let o=await Y(10);return console.log("\uD83D\uDCE6 Available products (".concat(o.length,"):"),null==o?void 0:o.slice(0,3)),{connectionWorking:!!t,categoriesCount:e.length,productsCount:o.length,categories:e.map(n=>({name:n.name,slug:n.slug,count:n.count})),sampleProducts:null==o?void 0:o.slice(0,3).map(n=>({name:n.name,slug:n.slug}))}}catch(n){return console.error("❌ WooCommerce connection test failed:",n),{connectionWorking:!1,error:n instanceof Error?n.message:"Unknown error"}}}async function nt(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];try{if(0===n.length)return{contents:{nodes:[]},subtotal:"0",total:"0",totalTax:"0",isEmpty:!0,contentsCount:0};let t=n[0],e=await no("",[t]);if(n.length>1){for(let t=1;t<n.length;t++)await no("",[n[t]]);return await ne()}return e}catch(n){throw console.error("Error creating cart:",n),n}}async function ne(){try{let n=await B({query:K,variables:{}});return(null==n?void 0:n.cart)||null}catch(n){return console.error("Error fetching cart:",n),null}}async function no(n,t){try{if(0===t.length)throw Error("No items provided to add to cart");let n=t[0],e={productId:parseInt(n.productId),quantity:n.quantity||1,variationId:n.variationId?parseInt(n.variationId):null,extraData:null};console.log("Adding to cart with variables:",e);let o=await B({query:X,variables:e});return console.log("Add to cart response:",o),o.addToCart.cart}catch(n){throw console.error("Error adding items to cart:",n),n}}async function nr(n,t){try{var e;let n=await B({query:z,variables:{keys:t,all:!1}});return(null==n?void 0:null===(e=n.removeItemsFromCart)||void 0===e?void 0:e.cart)||null}catch(n){throw console.error("Error removing items from cart:",n),n}}function na(n){var t,e,o,r,a,i,s,u,c,l;if(!n)return null;let d=!!(null===(e=n.variations)||void 0===e?void 0:null===(t=e.nodes)||void 0===t?void 0:t.length),p={minVariantPrice:{amount:n.price||"0",currencyCode:"INR"},maxVariantPrice:{amount:n.price||"0",currencyCode:"INR"}};if(d&&(null===(r=n.variations)||void 0===r?void 0:null===(o=r.nodes)||void 0===o?void 0:o.length)>0){let t=n.variations.nodes.map(n=>parseFloat(n.price||"0")).filter(n=>!isNaN(n));t.length>0&&(p={minVariantPrice:{amount:String(Math.min(...t)),currencyCode:"INR"},maxVariantPrice:{amount:String(Math.max(...t)),currencyCode:"INR"}})}let m=function(n){var t,e;let o=[];return n.image&&o.push({url:n.image.sourceUrl,altText:n.image.altText||n.name||""}),(null===(e=n.galleryImages)||void 0===e?void 0:null===(t=e.nodes)||void 0===t?void 0:t.length)&&n.galleryImages.nodes.forEach(t=>{n.image&&t.sourceUrl===n.image.sourceUrl||o.push({url:t.sourceUrl,altText:t.altText||n.name||""})}),o}(n),g=(null===(i=n.variations)||void 0===i?void 0:null===(a=i.nodes)||void 0===a?void 0:a.map(n=>{var t,e;return{id:n.id,title:n.name,price:{amount:n.price||"0",currencyCode:"USD"},availableForSale:"IN_STOCK"===n.stockStatus,selectedOptions:(null===(e=n.attributes)||void 0===e?void 0:null===(t=e.nodes)||void 0===t?void 0:t.map(n=>({name:n.name,value:n.value})))||[],sku:n.sku||"",image:n.image?{url:n.image.sourceUrl,altText:n.image.altText||""}:null}}))||[],f=(null===(u=n.attributes)||void 0===u?void 0:null===(s=u.nodes)||void 0===s?void 0:s.map(n=>({name:n.name,values:n.options||[]})))||[],y=(null===(l=n.productCategories)||void 0===l?void 0:null===(c=l.nodes)||void 0===c?void 0:c.map(n=>({handle:n.slug,title:n.name})))||[],h={};return n.metafields&&n.metafields.forEach(n=>{h[n.key]=n.value}),{id:n.id,handle:n.slug,title:n.name,description:n.description||"",descriptionHtml:n.description||"",priceRange:p,options:f,variants:g,images:m,collections:y,availableForSale:"OUT_OF_STOCK"!==n.stockStatus,metafields:h,_originalWooProduct:n}}(0,r.Ps)(I()),(0,r.Ps)(b());let ni=function(n,t,e){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";if(!n||!n.metafields)return o;if(e){let r="".concat(e,":").concat(t);return n.metafields[r]||o}return n.metafields[t]||o};function ns(n){var t,e,o,r;if(!n)return null;let a=(null===(e=n.contents)||void 0===e?void 0:null===(t=e.nodes)||void 0===t?void 0:t.map(n=>{var t,e,o,r;let a=null===(t=n.product)||void 0===t?void 0:t.node,i=null===(e=n.variation)||void 0===e?void 0:e.node;return{id:n.key,quantity:n.quantity,merchandise:{id:(null==i?void 0:i.id)||(null==a?void 0:a.id),title:(null==i?void 0:i.name)||(null==a?void 0:a.name),product:{id:null==a?void 0:a.id,handle:null==a?void 0:a.slug,title:null==a?void 0:a.name,image:(null==a?void 0:a.image)?{url:null==a?void 0:a.image.sourceUrl,altText:(null==a?void 0:a.image.altText)||""}:null},selectedOptions:(null==i?void 0:null===(r=i.attributes)||void 0===r?void 0:null===(o=r.nodes)||void 0===o?void 0:o.map(n=>({name:n.name,value:n.value})))||[]},cost:{totalAmount:{amount:n.total||"0",currencyCode:"USD"}}}}))||[],i=(null===(r=n.appliedCoupons)||void 0===r?void 0:null===(o=r.nodes)||void 0===o?void 0:o.map(n=>({code:n.code,amount:n.discountAmount||"0"})))||[],s=a.reduce((n,t)=>n+t.quantity,0);return{id:n.id,checkoutUrl:"",totalQuantity:s,cost:{subtotalAmount:{amount:n.subtotal||"0",currencyCode:"USD"},totalAmount:{amount:n.total||"0",currencyCode:"USD"}},lines:a,discountCodes:i}}function nu(n){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],e="".concat(x.storeUrl,"/checkout"),o=n?"?cart=".concat(n):"",r="";return t||(r="".concat(o?"&":"?","guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1")),"".concat(e).concat(o).concat(r)}async function nc(n){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:60;try{if(!n||"undefined"===n||"null"===n)return console.warn("Invalid product ID format: ".concat(n,", returning fallback product")),nl(n);let e=await (0,a.xh)(n),o=(0,r.Ps)(D());try{let r=await F(o,{id:e},["product-".concat(e),"products","inventory"],t);if(!(null==r?void 0:r.product))return console.warn("No product found with ID: ".concat(n,", returning fallback product")),nl(n);return r.product}catch(t){return console.error("Error fetching product with ID ".concat(n,":"),t),nl(n)}}catch(t){return console.error("Error in getProductById for ID ".concat(n,":"),t),nl(n)}}function nl(n){return{id:n,databaseId:0,name:"Product Not Found",slug:"product-not-found",description:"This product is no longer available.",shortDescription:"Product not found",price:"0.00",regularPrice:"0.00",salePrice:null,onSale:!1,stockStatus:"OUT_OF_STOCK",stockQuantity:0,image:{id:null,sourceUrl:"/placeholder-product.jpg",altText:"Product not found"},productCategories:{nodes:[]}}}async function nd(n){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},e="number"==typeof t?t:t.first||10,o=(0,r.Ps)(S());try{let t=await Q.request(o,{query:n,first:e});return(null==t?void 0:t.products)||{nodes:[],pageInfo:{hasNextPage:!1,endCursor:null}}}catch(n){return console.error("Error searching products:",n),{nodes:[],pageInfo:{hasNextPage:!1,endCursor:null}}}}async function np(n){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let{first:e=20}=t;console.log('\uD83D\uDD0D Fetching category products for slug: "'.concat(n,'" with first: ').concat(e)),console.log("\uD83D\uDCE1 Using GraphQL endpoint: ".concat(i.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql"));let o=await B({query:j,variables:{slug:n,first:e}});if(console.log('\uD83D\uDCCA Raw response for category "'.concat(n,'":'),JSON.stringify(o,null,2)),!(null==o?void 0:o.productCategory)&&(console.log('⚠️ No productCategory found in response for slug: "'.concat(n,'"')),n&&!isNaN(Number(n)))){console.log("\uD83D\uDD04 Trying to fetch category by ID: ".concat(n));let t=await B({query:j.replace("idType: SLUG","idType: DATABASE_ID"),variables:{slug:Number(n),first:e}});return console.log("\uD83D\uDCCA Response by ID:",JSON.stringify(t,null,2)),(null==t?void 0:t.productCategory)||null}return(null==o?void 0:o.productCategory)||null}catch(t){return console.error("❌ Error fetching category products with slug ".concat(n,":"),t),t instanceof Error&&(console.error("Error message: ".concat(t.message)),console.error("Error stack: ".concat(t.stack))),null}}(0,r.Ps)(k()),(0,r.Ps)(T()),(0,r.Ps)(N()),(0,r.Ps)($()),(0,r.Ps)(_()),(0,r.Ps)(E()),(0,r.Ps)(U());let nm=(0,r.Ps)(q());async function ng(n){try{return(await B({query:nm,variables:{input:{items:n}}})).updateItemQuantities.cart}catch(n){throw console.error("Error updating cart:",n),n}}}}]);