"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5131],{18403:function(e,t,n){n.d(t,{V:function(){return o}});var r,o=function(){return r||n.nc}},47692:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},58293:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(39763).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},72792:function(e,t,n){let r;n.d(t,{x8:function(){return eC},VY:function(){return eD},aV:function(){return ex},h_:function(){return ew},fC:function(){return eh},xz:function(){return eb}});var o,i=n(2265),u=n(6741),a=n(98575),l=n(73966),s=n(99255),d=n(80886),c=n(66840),f=n(26606),v=n(57437),p="dismissableLayer.update",m=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),y=i.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:l=!1,onEscapeKeyDown:s,onPointerDownOutside:d,onFocusOutside:y,onInteractOutside:h,onDismiss:b,...w}=e,x=i.useContext(m),[D,C]=i.useState(null),R=null!==(r=null==D?void 0:D.ownerDocument)&&void 0!==r?r:null===(n=globalThis)||void 0===n?void 0:n.document,[,N]=i.useState({}),k=(0,a.e)(t,e=>C(e)),L=Array.from(x.layers),[A]=[...x.layersWithOutsidePointerEventsDisabled].slice(-1),W=L.indexOf(A),j=D?L.indexOf(D):-1,O=x.layersWithOutsidePointerEventsDisabled.size>0,P=j>=W,F=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,f.W)(e),o=i.useRef(!1),u=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){E("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",u.current),u.current=t,n.addEventListener("click",u.current,{once:!0})):t()}else n.removeEventListener("click",u.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",u.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...x.branches].some(e=>e.contains(t));!P||n||(null==d||d(e),null==h||h(e),e.defaultPrevented||null==b||b())},R),M=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,f.W)(e),o=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!o.current&&E("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;[...x.branches].some(e=>e.contains(t))||(null==y||y(e),null==h||h(e),e.defaultPrevented||null==b||b())},R);return!function(e,t=globalThis?.document){let n=(0,f.W)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{j!==x.layers.size-1||(null==s||s(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},R),i.useEffect(()=>{if(D)return l&&(0===x.layersWithOutsidePointerEventsDisabled.size&&(o=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),x.layersWithOutsidePointerEventsDisabled.add(D)),x.layers.add(D),g(),()=>{l&&1===x.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=o)}},[D,R,l,x]),i.useEffect(()=>()=>{D&&(x.layers.delete(D),x.layersWithOutsidePointerEventsDisabled.delete(D),g())},[D,x]),i.useEffect(()=>{let e=()=>N({});return document.addEventListener(p,e),()=>document.removeEventListener(p,e)},[]),(0,v.jsx)(c.WV.div,{...w,ref:k,style:{pointerEvents:O?P?"auto":"none":void 0,...e.style},onFocusCapture:(0,u.M)(e.onFocusCapture,M.onFocusCapture),onBlurCapture:(0,u.M)(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:(0,u.M)(e.onPointerDownCapture,F.onPointerDownCapture)})});function g(){let e=new CustomEvent(p);document.dispatchEvent(e)}function E(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,u=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,c.jH)(i,u):i.dispatchEvent(u)}y.displayName="DismissableLayer",i.forwardRef((e,t)=>{let n=i.useContext(m),r=i.useRef(null),o=(0,a.e)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,v.jsx)(c.WV.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var h="focusScope.autoFocusOnMount",b="focusScope.autoFocusOnUnmount",w={bubbles:!1,cancelable:!0},x=i.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:u,...l}=e,[s,d]=i.useState(null),p=(0,f.W)(o),m=(0,f.W)(u),y=i.useRef(null),g=(0,a.e)(t,e=>d(e)),E=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(r){let e=function(e){if(E.paused||!s)return;let t=e.target;s.contains(t)?y.current=t:R(y.current,{select:!0})},t=function(e){if(E.paused||!s)return;let t=e.relatedTarget;null===t||s.contains(t)||R(y.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&R(s)});return s&&n.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,s,E.paused]),i.useEffect(()=>{if(s){N.add(E);let e=document.activeElement;if(!s.contains(e)){let t=new CustomEvent(h,w);s.addEventListener(h,p),s.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(R(r,{select:t}),document.activeElement!==n)return}(D(s).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&R(s))}return()=>{s.removeEventListener(h,p),setTimeout(()=>{let t=new CustomEvent(b,w);s.addEventListener(b,m),s.dispatchEvent(t),t.defaultPrevented||R(null!=e?e:document.body,{select:!0}),s.removeEventListener(b,m),N.remove(E)},0)}}},[s,p,m,E]);let x=i.useCallback(e=>{if(!n&&!r||E.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=D(e);return[C(t,e),C(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&R(i,{select:!0})):(e.preventDefault(),n&&R(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,E.paused]);return(0,v.jsx)(c.WV.div,{tabIndex:-1,...l,ref:g,onKeyDown:x})});function D(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function C(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function R(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}x.displayName="FocusScope";var N=(r=[],{add(e){let t=r[0];e!==t&&(null==t||t.pause()),(r=k(r,e)).unshift(e)},remove(e){var t;null===(t=(r=k(r,e))[0])||void 0===t||t.resume()}});function k(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var L=n(54887),A=n(61188),W=i.forwardRef((e,t)=>{var n,r;let{container:o,...u}=e,[a,l]=i.useState(!1);(0,A.b)(()=>l(!0),[]);let s=o||a&&(null===(r=globalThis)||void 0===r?void 0:null===(n=r.document)||void 0===n?void 0:n.body);return s?L.createPortal((0,v.jsx)(c.WV.div,{...u,ref:t}),s):null});W.displayName="Portal";var j=n(71599),O=0;function P(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var F=n(62904),M=new WeakMap,I=new WeakMap,T={},S=0,_=function(e){return e&&(e.host||_(e.parentNode))},V=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=_(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});T[n]||(T[n]=new WeakMap);var i=T[n],u=[],a=new Set,l=new Set(o),s=function(e){!e||a.has(e)||(a.add(e),s(e.parentNode))};o.forEach(s);var d=function(e){!e||l.has(e)||Array.prototype.forEach.call(e.children,function(e){if(a.has(e))d(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,l=(M.get(e)||0)+1,s=(i.get(e)||0)+1;M.set(e,l),i.set(e,s),u.push(e),1===l&&o&&I.set(e,!0),1===s&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return d(t),a.clear(),S++,function(){u.forEach(function(e){var t=M.get(e)-1,o=i.get(e)-1;M.set(e,t),i.set(e,o),t||(I.has(e)||e.removeAttribute(r),I.delete(e)),o||e.removeAttribute(n)}),--S||(M=new WeakMap,M=new WeakMap,I=new WeakMap,T={})}},z=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),V(r,o,n,"aria-hidden")):function(){return null}},K=n(37053),B="Dialog",[Z,H]=(0,l.b)(B),[q,U]=Z(B),Y=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:u,modal:a=!0}=e,l=i.useRef(null),c=i.useRef(null),[f=!1,p]=(0,d.T)({prop:r,defaultProp:o,onChange:u});return(0,v.jsx)(q,{scope:t,triggerRef:l,contentRef:c,contentId:(0,s.M)(),titleId:(0,s.M)(),descriptionId:(0,s.M)(),open:f,onOpenChange:p,onOpenToggle:i.useCallback(()=>p(e=>!e),[p]),modal:a,children:n})};Y.displayName=B;var G="DialogTrigger",J=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=U(G,n),i=(0,a.e)(t,o.triggerRef);return(0,v.jsx)(c.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":ev(o.open),...r,ref:i,onClick:(0,u.M)(e.onClick,o.onOpenToggle)})});J.displayName=G;var Q="DialogPortal",[X,$]=Z(Q,{forceMount:void 0}),ee=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:o}=e,u=U(Q,t);return(0,v.jsx)(X,{scope:t,forceMount:n,children:i.Children.map(r,e=>(0,v.jsx)(j.z,{present:n||u.open,children:(0,v.jsx)(W,{asChild:!0,container:o,children:e})}))})};ee.displayName=Q;var et="DialogOverlay",en=i.forwardRef((e,t)=>{let n=$(et,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=U(et,e.__scopeDialog);return i.modal?(0,v.jsx)(j.z,{present:r||i.open,children:(0,v.jsx)(er,{...o,ref:t})}):null});en.displayName=et;var er=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=U(et,n);return(0,v.jsx)(F.Z,{as:K.g7,allowPinchZoom:!0,shards:[o.contentRef],children:(0,v.jsx)(c.WV.div,{"data-state":ev(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),eo="DialogContent",ei=i.forwardRef((e,t)=>{let n=$(eo,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=U(eo,e.__scopeDialog);return(0,v.jsx)(j.z,{present:r||i.open,children:i.modal?(0,v.jsx)(eu,{...o,ref:t}):(0,v.jsx)(ea,{...o,ref:t})})});ei.displayName=eo;var eu=i.forwardRef((e,t)=>{let n=U(eo,e.__scopeDialog),r=i.useRef(null),o=(0,a.e)(t,n.contentRef,r);return i.useEffect(()=>{let e=r.current;if(e)return z(e)},[]),(0,v.jsx)(el,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,u.M)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,u.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,u.M)(e.onFocusOutside,e=>e.preventDefault())})}),ea=i.forwardRef((e,t)=>{let n=U(eo,e.__scopeDialog),r=i.useRef(!1),o=i.useRef(!1);return(0,v.jsx)(el,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var i,u;null===(i=e.onCloseAutoFocus)||void 0===i||i.call(e,t),t.defaultPrevented||(r.current||null===(u=n.triggerRef.current)||void 0===u||u.focus(),t.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:t=>{var i,u;null===(i=e.onInteractOutside)||void 0===i||i.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let a=t.target;(null===(u=n.triggerRef.current)||void 0===u?void 0:u.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),el=i.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:u,...l}=e,s=U(eo,n),d=i.useRef(null),c=(0,a.e)(t,d);return i.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:P()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:P()),O++,()=>{1===O&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),O--}},[]),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(x,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:u,children:(0,v.jsx)(y,{role:"dialog",id:s.contentId,"aria-describedby":s.descriptionId,"aria-labelledby":s.titleId,"data-state":ev(s.open),...l,ref:c,onDismiss:()=>s.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(eg,{titleId:s.titleId}),(0,v.jsx)(eE,{contentRef:d,descriptionId:s.descriptionId})]})]})}),es="DialogTitle";i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=U(es,n);return(0,v.jsx)(c.WV.h2,{id:o.titleId,...r,ref:t})}).displayName=es;var ed="DialogDescription";i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=U(ed,n);return(0,v.jsx)(c.WV.p,{id:o.descriptionId,...r,ref:t})}).displayName=ed;var ec="DialogClose",ef=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=U(ec,n);return(0,v.jsx)(c.WV.button,{type:"button",...r,ref:t,onClick:(0,u.M)(e.onClick,()=>o.onOpenChange(!1))})});function ev(e){return e?"open":"closed"}ef.displayName=ec;var ep="DialogTitleWarning",[em,ey]=(0,l.k)(ep,{contentName:eo,titleName:es,docsSlug:"dialog"}),eg=e=>{let{titleId:t}=e,n=ey(ep),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return i.useEffect(()=>{t&&!document.getElementById(t)&&console.error(r)},[r,t]),null},eE=e=>{let{contentRef:t,descriptionId:n}=e,r=ey("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return i.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(o)},[o,t,n]),null},eh=Y,eb=J,ew=ee,ex=en,eD=ei,eC=ef}}]);