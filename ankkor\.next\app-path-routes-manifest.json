{"/_not-found/page": "/_not-found", "/about/sustainability/page": "/about/sustainability", "/about/page": "/about", "/admin/products/page": "/admin/products", "/api/admin/migrate-inventory-mappings/route": "/api/admin/migrate-inventory-mappings", "/about/craftsmanship/page": "/about/craftsmanship", "/api/auth/route": "/api/auth", "/api/auth/update-profile/route": "/api/auth/update-profile", "/api/checkout/route": "/api/checkout", "/api/cache/products/[handle]/route": "/api/cache/products/[handle]", "/api/graphql/route": "/api/graphql", "/api/products/[id]/stock/route": "/api/products/[id]/stock", "/api/products/validate-stock/route": "/api/products/validate-stock", "/api/razorpay/create-order/route": "/api/razorpay/create-order", "/api/razorpay/test-connection/route": "/api/razorpay/test-connection", "/api/razorpay/verify-payment/route": "/api/razorpay/verify-payment", "/api/razorpay/test-order/route": "/api/razorpay/test-order", "/api/reconcile/route": "/api/reconcile", "/api/reservations/cleanup/route": "/api/reservations/cleanup", "/api/reservations/route": "/api/reservations", "/api/shipping-rates/route": "/api/shipping-rates", "/api/test/route": "/api/test", "/api/stock-updates/route": "/api/stock-updates", "/api/revalidate/route": "/api/revalidate", "/api/trigger-test/route": "/api/trigger-test", "/api/user/wishlist/route": "/api/user/wishlist", "/api/webhooks/order/route": "/api/webhooks/order", "/api/webhooks/inventory/route": "/api/webhooks/inventory", "/api/webhooks/test/route": "/api/webhooks/test", "/api/webhooks/simple/route": "/api/webhooks/simple", "/api/webhooks/route": "/api/webhooks", "/cart-test/page": "/cart-test", "/checkout/page": "/checkout", "/category/[slug]/page": "/category/[slug]", "/collection/polos/page": "/collection/polos", "/collection/shirts/page": "/collection/shirts", "/customer-service/contact/page": "/customer-service/contact", "/collection/page": "/collection", "/customer-service/faq/page": "/customer-service/faq", "/customer-service/page": "/customer-service", "/order-confirmed/page": "/order-confirmed", "/customer-service/size-guide/page": "/customer-service/size-guide", "/local-cart-test/page": "/local-cart-test", "/product/[slug]/page": "/product/[slug]", "/page": "/", "/privacy-policy/page": "/privacy-policy", "/return-policy/page": "/return-policy", "/robots.txt/route": "/robots.txt", "/shipping-policy/page": "/shipping-policy", "/search/page": "/search", "/terms-of-service/page": "/terms-of-service", "/test-auth/page": "/test-auth", "/test-auth/success/page": "/test-auth/success", "/test/page": "/test", "/woocommerce-cart-test/page": "/woocommerce-cart-test", "/test-woo/page": "/test-woo", "/wishlist/page": "/wishlist", "/woocommerce-test/success/page": "/woocommerce-test/success", "/woocommerce-checkout-test/page": "/woocommerce-checkout-test", "/woocommerce-test/page": "/woocommerce-test", "/sitemap.xml/route": "/sitemap.xml", "/api/ankkor/v1/nonce/route": "/api/ankkor/v1/nonce", "/account/page": "/account", "/api/cron/inventory-sync/route": "/api/cron/inventory-sync", "/api/auth/me/route": "/api/auth/me", "/api/debug/route": "/api/debug", "/api/nonce/route": "/api/nonce", "/api/auth/user/route": "/api/auth/user", "/api/products/route": "/api/products", "/categories/page": "/categories", "/api/woo-sync/route": "/api/woo-sync", "/sign-up/page": "/sign-up", "/sign-in/page": "/sign-in"}