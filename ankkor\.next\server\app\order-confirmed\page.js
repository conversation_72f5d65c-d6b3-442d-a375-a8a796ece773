(()=>{var e={};e.id=1978,e.ids=[1978],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14177:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.Z,__next_app__:()=>u,originalPathname:()=>d,pages:()=>c,routeModule:()=>m,tree:()=>o}),t(76469),t(11360),t(7629),t(11930),t(12523);var s=t(23191),n=t(88716),a=t(43315),i=t(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let o=["",{children:["order-confirmed",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,76469)),"E:\\ankkorwoo\\ankkor\\src\\app\\order-confirmed\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,11360)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,7629)),"E:\\ankkorwoo\\ankkor\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,11930)),"E:\\ankkorwoo\\ankkor\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],c=["E:\\ankkorwoo\\ankkor\\src\\app\\order-confirmed\\page.tsx"],d="/order-confirmed/page",u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/order-confirmed/page",pathname:"/order-confirmed",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},64305:(e,r,t)=>{Promise.resolve().then(t.bind(t,40927))},28916:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(76557).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},48705:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(76557).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},14228:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(76557).Z)("Truck",[["path",{d:"M5 18H3c-.6 0-1-.4-1-1V7c0-.6.4-1 1-1h10c.6 0 1 .4 1 1v11",key:"hs4xqm"}],["path",{d:"M14 9h4l4 4v4c0 .6-.4 1-1 1h-2",key:"11fp61"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}]])},40927:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var s=t(10326),n=t(17577),a=t(35047),i=t(54659),l=t(28916),o=t(48705),c=t(14228),d=t(72248);function u(){let e=(0,a.useRouter)();(0,a.useSearchParams)();let[r,t]=(0,n.useState)(null);return r?s.jsx("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"max-w-2xl mx-auto text-center",children:[s.jsx("div",{className:"mb-8",children:s.jsx("div",{className:"mx-auto w-24 h-24 bg-green-100 rounded-full flex items-center justify-center",children:s.jsx(i.Z,{className:"w-12 h-12 text-green-600"})})}),(0,s.jsxs)("div",{className:"mb-8",children:[s.jsx("h1",{className:"text-3xl font-serif mb-4 text-gray-900",children:"Thank You for Your Order!"}),s.jsx("p",{className:"text-lg text-gray-600 mb-6",children:"Your order has been successfully placed and is being processed."}),(0,s.jsxs)("div",{className:"bg-gray-50 border rounded-lg p-6 mb-6",children:[s.jsx("h2",{className:"text-lg font-medium mb-2",children:"Order Details"}),(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[s.jsx("span",{className:"text-gray-600",children:"Order ID:"}),(0,s.jsxs)("span",{className:"font-mono text-lg font-medium text-[#2c2c27]",children:["#",r]})]})]})]}),(0,s.jsxs)("div",{className:"mb-8",children:[s.jsx("h3",{className:"text-xl font-medium mb-6",children:"What happens next?"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-3",children:s.jsx(l.Z,{className:"w-6 h-6 text-blue-600"})}),s.jsx("h4",{className:"font-medium mb-2",children:"Payment Confirmed"}),s.jsx("p",{className:"text-sm text-gray-600",children:"Your payment has been successfully processed"})]}),(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"mx-auto w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-3",children:s.jsx(o.Z,{className:"w-6 h-6 text-yellow-600"})}),s.jsx("h4",{className:"font-medium mb-2",children:"Order Processing"}),s.jsx("p",{className:"text-sm text-gray-600",children:"We're preparing your items for shipment"})]}),(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-3",children:s.jsx(c.Z,{className:"w-6 h-6 text-green-600"})}),s.jsx("h4",{className:"font-medium mb-2",children:"On the Way"}),s.jsx("p",{className:"text-sm text-gray-600",children:"Your order will be shipped soon"})]})]})]}),(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8",children:[s.jsx("h3",{className:"font-medium mb-2",children:"Order Confirmation Email"}),s.jsx("p",{className:"text-sm text-gray-600",children:"We've sent an order confirmation email with your order details and tracking information. Please check your inbox and spam folder."})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx(d.z,{onClick:()=>e.push("/"),className:"w-full md:w-auto bg-[#2c2c27] hover:bg-[#3c3c37] text-white px-8 py-3",children:"Continue Shopping"}),s.jsx("div",{className:"text-center",children:s.jsx("button",{onClick:()=>e.push("/account"),className:"text-[#2c2c27] hover:underline text-sm",children:"View Order History"})})]}),(0,s.jsxs)("div",{className:"mt-12 pt-8 border-t border-gray-200",children:[s.jsx("h3",{className:"font-medium mb-4",children:"Need Help?"}),s.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"If you have any questions about your order, please don't hesitate to contact us."}),(0,s.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("p",{children:[s.jsx("span",{className:"font-medium",children:"Email:"})," ",s.jsx("a",{href:"mailto:<EMAIL>",className:"text-[#2c2c27] hover:underline",children:"<EMAIL>"})]}),(0,s.jsxs)("p",{children:[s.jsx("span",{className:"font-medium",children:"Phone:"})," ",s.jsx("a",{href:"tel:+**********",className:"text-[#2c2c27] hover:underline",children:"+91 12345 67890"})]})]})]})]})}):null}function m(){return s.jsx(n.Suspense,{fallback:s.jsx("div",{className:"container mx-auto py-12 px-4",children:s.jsx("div",{className:"text-center",children:"Loading..."})}),children:s.jsx(u,{})})}},72248:(e,r,t)=>{"use strict";let s,n;t.d(r,{z:()=>m});var a=t(10326);t(17577);var i=t(34214),l=t(41135);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,c=l.W;var d=t(51223);let u=(s="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",n={variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}},e=>{var r;if((null==n?void 0:n.variants)==null)return c(s,null==e?void 0:e.class,null==e?void 0:e.className);let{variants:t,defaultVariants:a}=n,i=Object.keys(t).map(r=>{let s=null==e?void 0:e[r],n=null==a?void 0:a[r];if(null===s)return null;let i=o(s)||o(n);return t[r][i]}),l=e&&Object.entries(e).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return c(s,i,null==n?void 0:null===(r=n.compoundVariants)||void 0===r?void 0:r.reduce((e,r)=>{let{class:t,className:s,...n}=r;return Object.entries(n).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...a,...l}[r]):({...a,...l})[r]===t})?[...e,t,s]:e},[]),null==e?void 0:e.class,null==e?void 0:e.className)});function m({className:e,variant:r,size:t,asChild:s=!1,...n}){let l=s?i.g7:"button";return a.jsx(l,{"data-slot":"button",className:(0,d.cn)(u({variant:r,size:t,className:e})),...n})}},51223:(e,r,t)=>{"use strict";t.d(r,{cn:()=>a});var s=t(41135),n=t(31009);function a(...e){return(0,n.m6)((0,s.W)(e))}},76469:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\order-confirmed\page.tsx#default`)},48051:(e,r,t)=>{"use strict";t.d(r,{F:()=>a,e:()=>i});var s=t(17577);function n(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function a(...e){return r=>{let t=!1,s=e.map(e=>{let s=n(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():n(e[r],null)}}}}function i(...e){return s.useCallback(a(...e),e)}},34214:(e,r,t)=>{"use strict";t.d(r,{g7:()=>i});var s=t(17577),n=t(48051),a=t(10326),i=s.forwardRef((e,r)=>{let{children:t,...n}=e,i=s.Children.toArray(t),o=i.find(c);if(o){let e=o.props.children,t=i.map(r=>r!==o?r:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,a.jsx)(l,{...n,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,t):null})}return(0,a.jsx)(l,{...n,ref:r,children:t})});i.displayName="Slot";var l=s.forwardRef((e,r)=>{let{children:t,...a}=e;if(s.isValidElement(t)){let e=function(e){let r=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,t=r&&"isReactWarning"in r&&r.isReactWarning;return t?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(t),i=function(e,r){let t={...r};for(let s in r){let n=e[s],a=r[s];/^on[A-Z]/.test(s)?n&&a?t[s]=(...e)=>{a(...e),n(...e)}:n&&(t[s]=n):"style"===s?t[s]={...n,...a}:"className"===s&&(t[s]=[n,a].filter(Boolean).join(" "))}return{...e,...t}}(a,t.props);return t.type!==s.Fragment&&(i.ref=r?(0,n.F)(r,e):e),s.cloneElement(t,i)}return s.Children.count(t)>1?s.Children.only(null):null});l.displayName="SlotClone";var o=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});function c(e){return s.isValidElement(e)&&e.type===o}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,1057,325,5436],()=>t(14177));module.exports=s})();