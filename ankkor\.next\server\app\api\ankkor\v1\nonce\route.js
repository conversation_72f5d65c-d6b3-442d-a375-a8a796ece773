"use strict";(()=>{var e={};e.id=483,e.ids=[483],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},85853:(e,n,t)=>{t.r(n),t.d(n,{originalPathname:()=>g,patchFetch:()=>w,requestAsyncStorage:()=>f,routeModule:()=>d,serverHooks:()=>k,staticGenerationAsyncStorage:()=>m});var r={};t.r(r),t.d(r,{GET:()=>u,dynamic:()=>s});var o=t(49303),a=t(88716),c=t(60670),i=t(87070);let s="force-dynamic";async function u(e){try{let n="https://maroon-lapwing-781450.hostingersite.com";if(!n)throw Error("WooCommerce URL not configured");let t=e.headers.get("Cart-Token"),r=await l(n,t);if(r||(r=await p(n,t)),r||(r=await h(n,t)),!r)throw Error("Could not obtain a valid nonce from any source");return i.NextResponse.json({nonce:r},{headers:{"Cache-Control":"no-store, max-age=0"}})}catch(e){return console.error("Error fetching WooCommerce nonce:",e),i.NextResponse.json({success:!1,message:e instanceof Error?e.message:"An error occurred fetching the nonce"},{status:500})}}async function l(e,n){try{let t={"Content-Type":"application/json"};n&&(t["Cart-Token"]=n);let r=await fetch(`${e}/wp-json/wc/store/v1/cart`,{method:"GET",headers:t,credentials:"include",cache:"no-store"}),o=r.headers.get("x-wc-store-api-nonce");if(o)return o;let a=await r.json();if(a.extensions&&a.extensions.store_api_nonce)return a.extensions.store_api_nonce;return null}catch(e){return console.error("Error fetching nonce from Store API:",e),null}}async function p(e,n){try{let t={"Content-Type":"application/json"};n&&(t["Cart-Token"]=n);let r=await fetch(`${e}/wp-json`,{method:"GET",headers:t,credentials:"include",cache:"no-store"});if(!r.ok)return null;let o=await r.json();if(o&&o.authentication&&o.authentication.nonce)return o.authentication.nonce;return null}catch(e){return console.error("Error fetching nonce from WP API:",e),null}}async function h(e,n){try{let t={"Content-Type":"application/json"};n&&(t["Cart-Token"]=n);let r=(await fetch(`${e}/wp-json/wc/store/v1/products`,{method:"GET",headers:t,cache:"no-store"})).headers.get("x-wc-store-api-nonce");if(r)return r;return null}catch(e){return console.error("Error fetching nonce from cart request:",e),null}}let d=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/ankkor/v1/nonce/route",pathname:"/api/ankkor/v1/nonce",filename:"route",bundlePath:"app/api/ankkor/v1/nonce/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\ankkor\\v1\\nonce\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:f,staticGenerationAsyncStorage:m,serverHooks:k}=d,g="/api/ankkor/v1/nonce/route";function w(){return(0,c.patchFetch)({serverHooks:k,staticGenerationAsyncStorage:m})}}};var n=require("../../../../../webpack-runtime.js");n.C(e);var t=e=>n(n.s=e),r=n.X(0,[8948,5972],()=>t(85853));module.exports=r})();