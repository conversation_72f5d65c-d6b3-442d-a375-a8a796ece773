(()=>{var e={};e.id=2797,e.ids=[2797],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},33777:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.Z,__next_app__:()=>u,originalPathname:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>l}),t(4856),t(11360),t(7629),t(11930),t(12523);var s=t(23191),a=t(88716),n=t(43315),o=t(95231),i={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);t.d(r,i);let l=["",{children:["search",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4856)),"E:\\ankkorwoo\\ankkor\\src\\app\\search\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,11360)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,7629)),"E:\\ankkorwoo\\ankkor\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,11930)),"E:\\ankkorwoo\\ankkor\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],c=["E:\\ankkorwoo\\ankkor\\src\\app\\search\\page.tsx"],d="/search/page",u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/search/page",pathname:"/search",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},92842:(e,r,t)=>{Promise.resolve().then(t.bind(t,76519))},46226:(e,r,t)=>{"use strict";t.d(r,{default:()=>a.a});var s=t(69029),a=t.n(s)},69029:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{default:function(){return l},getImageProps:function(){return i}});let s=t(91174),a=t(23078),n=t(92481),o=s._(t(86820));function i(e){let{props:r}=(0,a.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,t]of Object.entries(r))void 0===t&&delete r[e];return{props:r}}let l=n.Image},76519:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>p});var a=t(10326),n=t(17577),o=t(35047),i=t(15725),l=t(53471),c=t(75290),d=e([i,l]);function u(){let e=(0,o.useSearchParams)().get("q")||"",[r,t]=(0,n.useState)([]),[s,i]=(0,n.useState)(!0),[d,u]=(0,n.useState)(null);return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[a.jsx("h1",{className:"text-3xl font-serif mb-2",children:"Search Results"}),e&&(0,a.jsxs)("p",{className:"text-gray-600 mb-8",children:['Showing results for "',e,'"']}),s?a.jsx("div",{className:"flex justify-center items-center py-16",children:a.jsx(c.Z,{className:"h-8 w-8 animate-spin text-gray-500"})}):d?a.jsx("div",{className:"bg-red-50 text-red-700 p-4 rounded-md",children:d}):0===r.length?(0,a.jsxs)("div",{className:"py-16 text-center",children:[a.jsx("p",{className:"text-xl text-gray-500 mb-4",children:"No products found"}),a.jsx("p",{className:"text-gray-500",children:"Try using different keywords or check for spelling errors"})]}):a.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:r.map(e=>a.jsx(l.Z,{id:e.id,name:e.name,price:e.salePrice||e.price,image:e.image?.sourceUrl||"",slug:e.slug,stockStatus:e.stockStatus,regularPrice:e.regularPrice,salePrice:e.salePrice,onSale:e.onSale||!1,shortDescription:e.shortDescription,type:e.type},e.id))})]})}function p(){return a.jsx(n.Suspense,{fallback:a.jsx("div",{className:"container mx-auto py-12 px-4",children:a.jsx("div",{className:"text-center",children:"Loading..."})}),children:a.jsx(u,{})})}[i,l]=d.then?(await d)():d,s()}catch(e){s(e)}})},4856:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\search\page.tsx#default`)}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,1057,2481,8578,5436,5725,5916,3471],()=>t(33777));module.exports=s})();