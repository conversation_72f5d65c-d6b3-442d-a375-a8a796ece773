(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9153],{98214:function(e,o,t){Promise.resolve().then(t.bind(t,22313))},22313:function(e,o,t){"use strict";t.r(o),t.d(o,{default:function(){return g}});var l=t(57437),i=t(2265),n=t(33145),c=t(43886),a=t(62670),r=t(69140),s=t(3697),d=t(82372),u=t(82429);function g(){let[e,o]=(0,i.useState)([]),[g,v]=(0,i.useState)(!0),[h,m]=(0,i.useState)(null),[p,f]=(0,i.useState)(null);(0,s.Z)(g,"fabric"),(0,i.useEffect)(()=>{(async()=>{try{var e,l,i,n,c,a,r,s,u,g,h,p,x,D,y,C,w,b;v(!0),m(null),console.log("\uD83D\uDD0D Starting to fetch shirts from WooCommerce...");let N=null;try{console.log("\uD83E\uDDEA Testing WooCommerce connection...");let{testWooCommerceConnection:e}=await Promise.resolve().then(t.bind(t,82372));N=await e(),console.log("\uD83D\uDD17 Connection test result:",N)}catch(e){console.log("❌ Failed to test connection:",e)}let j=null;try{console.log("\uD83D\uDCCB Fetching all categories to debug...");let{getAllCategories:e}=await Promise.resolve().then(t.bind(t,82372));j=await e(50),console.log("\uD83D\uDCC2 Available categories:",null==j?void 0:j.map(e=>({name:e.name,slug:e.slug,id:e.id,count:e.count})))}catch(e){console.log("❌ Failed to fetch categories:",e)}let P=null,S="";try{console.log('\uD83D\uDCCB Attempting to fetch with category slug: "shirts"'),P=await (0,d.getCategoryProducts)("shirts",{first:100}),S="slug: shirts",(null==P?void 0:null===(D=P.products)||void 0===D?void 0:null===(x=D.nodes)||void 0===x?void 0:x.length)>0?console.log("✅ Success with method 1 (slug: shirts)"):console.log("⚠️ Method 1 returned empty or null:",P)}catch(e){console.log("❌ Method 1 failed:",e)}if(!(null==P?void 0:null===(l=P.products)||void 0===l?void 0:null===(e=l.nodes)||void 0===e?void 0:e.length))for(let e of["shirt","Shirts","SHIRTS","men-shirts","mens-shirts","clothing","apparel"])try{if(console.log('\uD83D\uDCCB Attempting to fetch with category: "'.concat(e,'"')),P=await (0,d.getCategoryProducts)(e,{first:100}),S="slug: ".concat(e),(null==P?void 0:null===(C=P.products)||void 0===C?void 0:null===(y=C.nodes)||void 0===y?void 0:y.length)>0){console.log("✅ Success with alternative name: ".concat(e));break}console.log("⚠️ No products found for category: ".concat(e))}catch(o){console.log("❌ Failed with ".concat(e,":"),o)}if(!(null==P?void 0:null===(n=P.products)||void 0===n?void 0:null===(i=n.nodes)||void 0===i?void 0:i.length)&&(null==j?void 0:j.length)>0){console.log("\uD83D\uDCCB Searching for shirt-related categories in available categories...");let e=j.find(e=>{var o,t;let l=(null===(o=e.name)||void 0===o?void 0:o.toLowerCase())||"",i=(null===(t=e.slug)||void 0===t?void 0:t.toLowerCase())||"";return l.includes("shirt")||i.includes("shirt")||l.includes("clothing")||i.includes("clothing")||l.includes("apparel")||i.includes("apparel")});if(e){console.log("\uD83D\uDCCB Found potential shirt category: ".concat(e.name," (").concat(e.slug,")"));try{P=await (0,d.getCategoryProducts)(e.slug,{first:100}),S="found category: ".concat(e.slug),(null==P?void 0:null===(b=P.products)||void 0===b?void 0:null===(w=b.nodes)||void 0===w?void 0:w.length)>0&&console.log("✅ Success with found category: ".concat(e.slug))}catch(o){console.log("❌ Failed with found category ".concat(e.slug,":"),o)}}}if(!(null==P?void 0:null===(a=P.products)||void 0===a?void 0:null===(c=a.nodes)||void 0===c?void 0:c.length))try{console.log("\uD83D\uDCCB Attempting to fetch all products and filter by keywords...");let{getAllProducts:e}=await Promise.resolve().then(t.bind(t,82372)),o=await e(100);if(S="all products filtered by keywords",(null==o?void 0:o.length)>0){let e=o.filter(e=>{var o,t,l,i,n;let c=(null===(o=e.name)||void 0===o?void 0:o.toLowerCase())||(null===(t=e.title)||void 0===t?void 0:t.toLowerCase())||"",a=(null===(l=e.description)||void 0===l?void 0:l.toLowerCase())||(null===(i=e.shortDescription)||void 0===i?void 0:i.toLowerCase())||"",r=(null===(n=e.productCategories)||void 0===n?void 0:n.nodes)||e.categories||[],s=["shirt","formal","casual","dress","button","collar","sleeve"].some(e=>c.includes(e)||a.includes(e)),d=r.some(e=>{var o,t;let l=(null===(o=e.name)||void 0===o?void 0:o.toLowerCase())||(null===(t=e.slug)||void 0===t?void 0:t.toLowerCase())||"";return l.includes("shirt")||l.includes("clothing")||l.includes("apparel")});return s||d});P={products:{nodes:e}},console.log("✅ Filtered ".concat(e.length," shirt products from all products"))}}catch(e){console.log("❌ Method 4 failed:",e)}if(f({fetchMethod:S,totalProducts:(null==P?void 0:null===(s=P.products)||void 0===s?void 0:null===(r=s.nodes)||void 0===r?void 0:r.length)||0,connectionTest:N||"No connection test performed",availableCategories:(null==j?void 0:j.map(e=>({name:e.name,slug:e.slug,count:e.count})))||[],categoryData:P?JSON.stringify(P,null,2):"No data",timestamp:new Date().toISOString()}),console.log("\uD83D\uDCCA Debug Info:",{fetchMethod:S,totalProducts:(null==P?void 0:null===(g=P.products)||void 0===g?void 0:null===(u=g.nodes)||void 0===u?void 0:u.length)||0,hasData:!!P,hasProducts:!!(null==P?void 0:P.products),hasNodes:!!(null==P?void 0:null===(h=P.products)||void 0===h?void 0:h.nodes),availableCategories:(null==j?void 0:j.length)||0}),!P||!(null===(p=P.products)||void 0===p?void 0:p.nodes)||0===P.products.nodes.length){console.log("❌ No shirt products found in any category"),m("We're experiencing technical difficulties. Please try again later."),v(!1);return}let _=P.products.nodes;console.log("\uD83D\uDCE6 Found ".concat(_.length," products, normalizing..."));let W=_.map((e,o)=>{try{console.log("\uD83D\uDD04 Normalizing product ".concat(o+1,":"),e.name||e.title);let t=(0,d.Op)(e);if(t)return t.currencyCode="INR",console.log("✅ Successfully normalized: ".concat(t.title)),t;return console.log("⚠️ Failed to normalize product: ".concat(e.name||e.title)),null}catch(e){return console.error("❌ Error normalizing product ".concat(o+1,":"),e),null}}).filter(Boolean);console.log("\uD83C\uDF89 Successfully processed ".concat(W.length," shirt products")),console.log("\uD83D\uDCE6 Setting products:",W.map(e=>{var o,t;return{title:e.title,price:null===(t=e.priceRange)||void 0===t?void 0:null===(o=t.minVariantPrice)||void 0===o?void 0:o.amount,id:e.id}})),o(W)}catch(e){console.error("\uD83D\uDCA5 Critical error fetching products:",e),m("We're experiencing technical difficulties. Please try again later.")}finally{v(!1)}})()},[]);let x=[...e].sort((e,o)=>{var t,l,i,n;let c=(null===(t=e._originalWooProduct)||void 0===t?void 0:t.dateCreated)||(null===(l=e._originalWooProduct)||void 0===l?void 0:l.date_created)||e.id,a=(null===(i=o._originalWooProduct)||void 0===i?void 0:i.dateCreated)||(null===(n=o._originalWooProduct)||void 0===n?void 0:n.date_created)||o.id;return c&&a&&c!==e.id&&a!==o.id?new Date(a).getTime()-new Date(c).getTime():o.id.localeCompare(e.id)}),D={initial:{opacity:0,y:20},animate:{opacity:1,y:0,transition:{duration:.5}},exit:{opacity:0,y:20,transition:{duration:.3}}};return(0,l.jsxs)("div",{className:"min-h-screen bg-[#f8f8f5] pt-8 pb-24",children:[(0,l.jsx)("div",{className:"container mx-auto px-4 mb-12",children:(0,l.jsxs)("div",{className:"text-center max-w-3xl mx-auto",children:[(0,l.jsx)("h1",{className:"text-4xl font-serif font-bold mb-4 text-[#2c2c27]",children:"Shirts Collection"}),(0,l.jsx)("p",{className:"text-[#5c5c52] mb-8",children:"Discover our meticulously crafted shirts, designed with premium fabrics and impeccable attention to detail."})]})}),(0,l.jsxs)("div",{className:"relative h-[300px] mb-16 overflow-hidden",children:[(0,l.jsx)(n.default,{src:"https://images.unsplash.com/photo-1552374196-1ab2a1c593e8?q=80",alt:"Ankkor Shirts Collection",fill:!0,sizes:"(max-width: 768px) 100vw, 50vw",className:"object-cover image-animate"}),(0,l.jsx)("div",{className:"absolute inset-0 bg-[#2c2c27] bg-opacity-30 flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center text-white",children:[(0,l.jsx)("h2",{className:"text-3xl font-serif font-bold mb-4",children:"Signature Shirts"}),(0,l.jsx)("p",{className:"text-lg max-w-xl mx-auto",children:"Impeccably tailored for the perfect fit"})]})})]}),(0,l.jsxs)("div",{className:"container mx-auto px-4",children:[h&&!g&&(0,l.jsx)("div",{className:"text-center py-16",children:(0,l.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,l.jsx)("h3",{className:"text-xl font-serif text-[#2c2c27] mb-4",children:"Service Temporarily Unavailable"}),(0,l.jsx)("p",{className:"text-[#5c5c52] mb-6",children:h}),(0,l.jsx)("button",{onClick:()=>window.location.reload(),className:"bg-[#2c2c27] text-white px-6 py-2 hover:bg-[#3d3d35] transition-colors",children:"Try Again"})]})}),g&&(0,l.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8",children:Array.from({length:9}).map((e,o)=>(0,l.jsxs)("div",{className:"animate-pulse",children:[(0,l.jsx)(r.O,{className:"w-full h-80 mb-4"}),(0,l.jsx)(r.O,{className:"w-3/4 h-4 mb-2"}),(0,l.jsx)(r.O,{className:"w-1/2 h-4"})]},o))}),(0,l.jsx)("div",{className:"flex justify-end items-center mb-8",children:(0,l.jsxs)("div",{className:"text-[#5c5c52] text-sm",children:[x.length," products"]})}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"hidden md:flex justify-between items-center mb-8",children:[(0,l.jsx)("h2",{className:"text-[#2c2c27] font-serif text-xl",children:"Shirts Collection"}),(0,l.jsxs)("div",{className:"text-[#5c5c52]",children:[x.length," products"]})]}),!g&&!h&&x.length>0&&(0,l.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8",children:x.map(e=>{var o,t,i,n,r,s,g,v,h,m,p;let f="",x=!1;try{if(e.variants&&e.variants.length>0){let o=e.variants[0];if(o&&o.id){if(f=o.id,x=!0,!f.startsWith("gid://shopify/ProductVariant/")){let o=f.replace(/\D/g,"");o?f="gid://shopify/ProductVariant/".concat(o):(console.warn("Cannot parse variant ID for product ".concat(e.title,": ").concat(f)),x=!1)}console.log("Product ".concat(e.title," using variant ID: ").concat(f))}}if(!x&&e.id&&e.id.includes("/")){let o=e.id.split("/"),t=o[o.length-1];t&&/^\d+$/.test(t)&&(f="gid://shopify/ProductVariant/".concat(t),console.warn("Using fallback variant ID for ".concat(e.title,": ").concat(f)),x=!0)}}catch(o){console.error("Error processing variant for product ".concat(e.title,":"),o),x=!1}return x||console.error("No valid variant ID found for product: ".concat(e.title)),(0,l.jsx)(c.E.div,{variants:D,initial:"initial",animate:"animate",exit:"exit",layout:!0,children:(0,l.jsx)(a.Z,{id:e.id,name:e.title,slug:e.handle,price:(null===(o=e._originalWooProduct)||void 0===o?void 0:o.salePrice)||(null===(t=e._originalWooProduct)||void 0===t?void 0:t.price)||(null===(n=e.priceRange)||void 0===n?void 0:null===(i=n.minVariantPrice)||void 0===i?void 0:i.amount)||"0",image:(null===(r=e.images[0])||void 0===r?void 0:r.url)||"",material:(0,d.mJ)(e,"custom_material",void 0,"Premium Fabric"),isNew:!0,stockStatus:(null===(s=e._originalWooProduct)||void 0===s?void 0:s.stockStatus)||"IN_STOCK",compareAtPrice:e.compareAtPrice,regularPrice:null===(g=e._originalWooProduct)||void 0===g?void 0:g.regularPrice,salePrice:null===(v=e._originalWooProduct)||void 0===v?void 0:v.salePrice,onSale:(null===(h=e._originalWooProduct)||void 0===h?void 0:h.onSale)||!1,currencySymbol:(0,u.jK)(e.currencyCode),currencyCode:e.currencyCode||"INR",shortDescription:null===(m=e._originalWooProduct)||void 0===m?void 0:m.shortDescription,type:null===(p=e._originalWooProduct)||void 0===p?void 0:p.type})},e.id)})}),!g&&!h&&0===x.length&&(0,l.jsx)("div",{className:"text-center py-16",children:(0,l.jsx)("p",{className:"text-[#5c5c52]",children:"No shirts available at the moment."})})]})]})]})}}},function(e){e.O(0,[5540,4822,8787,8261,1539,2188,6003,7231,2323,9960,7696,8002,7111,8989,4513,8476,8496,8966,3903,6076,4596,62,6271,8726,8133,8049,2870,632,6613,9965,5270,2647,9971,9290,6459,7800,4818,1729,5526,4615,7406,2532,2068,4595,3280,8790,1104,7158,7044,9482,6628,986,4754,1744],function(){return e(e.s=98214)}),_N_E=e.O()}]);