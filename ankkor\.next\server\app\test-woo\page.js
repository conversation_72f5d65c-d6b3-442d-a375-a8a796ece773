(()=>{var e={};e.id=2483,e.ids=[2483],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},26560:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>o.Z,__next_app__:()=>m,originalPathname:()=>c,pages:()=>i,routeModule:()=>x,tree:()=>d}),r(19542),r(11360),r(7629),r(11930),r(12523);var t=r(23191),a=r(88716),o=r(43315),n=r(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(s,l);let d=["",{children:["test-woo",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,19542)),"E:\\ankkorwoo\\ankkor\\src\\app\\test-woo\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,11360)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,7629)),"E:\\ankkorwoo\\ankkor\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,11930)),"E:\\ankkorwoo\\ankkor\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],i=["E:\\ankkorwoo\\ankkor\\src\\app\\test-woo\\page.tsx"],c="/test-woo/page",m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/test-woo/page",pathname:"/test-woo",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},66655:(e,s,r)=>{Promise.resolve().then(r.bind(r,44491))},44491:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var t=r(10326),a=r(17577);function o(){let[e,s]=(0,a.useState)(null),[r,o]=(0,a.useState)(!0),[n,l]=(0,a.useState)(null);return r?t.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),t.jsx("p",{className:"text-gray-600",children:"Testing WooCommerce connection..."})]})}):n?t.jsx("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[t.jsx("h1",{className:"text-3xl font-bold text-red-600 mb-6",children:"WooCommerce Test Failed"}),t.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:t.jsx("p",{className:"text-red-800",children:n})})]})}):t.jsx("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto",children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"WooCommerce Connection Test Results"}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[t.jsx("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"\uD83D\uDD17 Connection Test"}),t.jsx("div",{className:"bg-gray-50 rounded p-4",children:t.jsx("pre",{className:"text-sm overflow-auto",children:JSON.stringify(e?.connectionTest,null,2)})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:["\uD83D\uDCC2 Categories (",e?.categoriesCount||0," total)"]}),t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:e?.categories?.map((e,s)=>t.jsxs("div",{className:"bg-gray-50 rounded p-3",children:[t.jsx("h3",{className:"font-medium text-gray-900",children:e.name}),t.jsxs("p",{className:"text-sm text-gray-600",children:["Slug: ",e.slug]}),t.jsxs("p",{className:"text-sm text-gray-600",children:["Count: ",e.count||0]}),t.jsxs("p",{className:"text-sm text-gray-600",children:["ID: ",e.id]})]},s))})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[t.jsx("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"\uD83D\uDC54 Shirts Category Test"}),t.jsx("div",{className:"bg-gray-50 rounded p-4",children:t.jsx("pre",{className:"text-sm overflow-auto",children:JSON.stringify(e?.shirtsCategory,null,2)})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[t.jsx("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"\uD83D\uDD0D Alternative Category Tests"}),t.jsx("div",{className:"space-y-4",children:e?.alternativeTests?.map((e,s)=>t.jsxs("div",{className:"border rounded p-4",children:[t.jsxs("div",{className:"flex items-center justify-between mb-2",children:[t.jsxs("h3",{className:"font-medium text-gray-900",children:['Category: "',e.name,'"']}),t.jsx("span",{className:`px-2 py-1 rounded text-sm ${e.success?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.success?`✅ ${e.productCount} products`:"❌ Failed"})]}),e.error&&t.jsx("p",{className:"text-red-600 text-sm",children:e.error}),e.result&&t.jsxs("details",{className:"mt-2",children:[t.jsx("summary",{className:"cursor-pointer text-sm text-gray-600",children:"View raw result"}),t.jsx("pre",{className:"text-xs bg-gray-50 p-2 mt-2 rounded overflow-auto",children:JSON.stringify(e.result,null,2)})]})]},s))})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[t.jsx("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"\uD83D\uDCCA Raw Test Results"}),t.jsx("div",{className:"bg-gray-50 rounded p-4",children:t.jsx("pre",{className:"text-xs overflow-auto max-h-96",children:JSON.stringify(e,null,2)})})]})]})})}},19542:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\test-woo\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8948,1057,5436],()=>r(26560));module.exports=t})();