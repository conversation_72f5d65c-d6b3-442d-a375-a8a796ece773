"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7044],{57152:function(e,t,a){var n=a(57437),i=a(2265),s=a(33145),r=a(43886);t.Z=e=>{let{src:t,alt:a,width:o,height:l,fill:c=!1,sizes:d=c?"(max-width: 768px) 100vw, 50vw":void 0,priority:u=!1,className:f="",animate:h=!0,style:m={}}=e,[x,g]=(0,i.useState)(!0),[p,b]=(0,i.useState)(!1);return(0,n.jsxs)("div",{className:"relative overflow-hidden ".concat(f),style:{minHeight:c?"100%":void 0,height:c?"100%":void 0,...m},onMouseEnter:()=>b(!0),onMouseLeave:()=>b(!1),children:[x&&(0,n.jsx)(r.E.div,{className:"absolute inset-0 bg-[#f4f3f0]",initial:{opacity:1},animate:{opacity:[.5,.8,.5],backgroundPosition:["0% 0%","100% 100%"]},transition:{opacity:{duration:1.5,repeat:1/0,ease:"easeInOut"},backgroundPosition:{duration:1.5,repeat:1/0,ease:"easeInOut"}},style:{background:"linear-gradient(90deg, #f4f3f0, #e5e2d9, #f4f3f0)",backgroundSize:"200% 100%"}}),(0,n.jsx)(r.E.div,{className:"w-full h-full",animate:h&&p?{scale:1.05,filter:"brightness(1.1)"}:{scale:1,filter:"brightness(1)"},transition:{duration:.7,ease:"easeInOut"},children:(0,n.jsx)(s.default,{src:t,alt:a,width:o,height:l,fill:c,sizes:d,priority:u,className:"\n            ".concat(x?"opacity-0":"opacity-100"," \n            transition-opacity duration-500\n            ").concat(c?"object-cover":"","\n          "),onLoad:()=>g(!1)})})]})}},29456:function(e,t,a){a.d(t,{Z:function(){return o}});var n=a(57437);a(2265);var i=a(48131),s=a(43886),r=e=>{let{size:t="md",variant:a="thread",className:i=""}=e,r={sm:{container:"w-16 h-16",text:"text-xs"},md:{container:"w-24 h-24",text:"text-sm"},lg:{container:"w-32 h-32",text:"text-base"}};return"thread"===a?(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center ".concat(i),children:[(0,n.jsxs)("div",{className:"relative ".concat(r[t].container),children:[(0,n.jsx)(s.E.div,{className:"absolute inset-0 rounded-full border-2 border-[#e5e2d9]",style:{borderTopColor:"#2c2c27",borderRightColor:"#2c2c27"},animate:{rotate:360},transition:{duration:1.5,repeat:1/0,ease:"linear"}}),(0,n.jsx)(s.E.div,{className:"absolute inset-2 rounded-full border-2 border-[#e5e2d9]",style:{borderBottomColor:"#8a8778",borderLeftColor:"#8a8778"},animate:{rotate:-360},transition:{duration:2,repeat:1/0,ease:"linear"}}),(0,n.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,n.jsx)("div",{className:"w-2 h-2 rounded-full bg-[#2c2c27]"})})]}),(0,n.jsx)("p",{className:"mt-4 font-serif text-[#5c5c52] ".concat(r[t].text),children:"Loading Collection"})]}):"fabric"===a?(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center ".concat(i),children:[(0,n.jsxs)("div",{className:"relative ".concat(r[t].container," flex items-center justify-center"),children:[(0,n.jsx)(s.E.div,{className:"absolute w-1/3 h-1/3 bg-[#e5e2d9]",animate:{rotate:360,scale:[1,1.2,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut"}}),(0,n.jsx)(s.E.div,{className:"absolute w-1/3 h-1/3 bg-[#8a8778]",animate:{rotate:-360,scale:[1,.8,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut",delay:.3}}),(0,n.jsx)(s.E.div,{className:"absolute w-1/3 h-1/3 bg-[#2c2c27]",animate:{rotate:360,scale:[1,.8,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut",delay:.6}})]}),(0,n.jsx)("p",{className:"mt-4 font-serif text-[#5c5c52] ".concat(r[t].text),children:"Preparing Your Style"})]}):"button"===a?(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center ".concat(i),children:[(0,n.jsx)("div",{className:"relative ".concat(r[t].container," flex items-center justify-center"),children:(0,n.jsx)("div",{className:"relative flex",children:[0,1,2,3].map(e=>(0,n.jsx)(s.E.div,{className:"w-3 h-3 mx-1 rounded-full bg-[#2c2c27] border border-[#8a8778]",animate:{y:[0,-10,0],opacity:[.5,1,.5]},transition:{duration:1,repeat:1/0,ease:"easeInOut",delay:.2*e}},e))})}),(0,n.jsx)("p",{className:"mt-4 font-serif text-[#5c5c52] ".concat(r[t].text),children:"Tailoring Experience"})]}):(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center ".concat(i),children:[(0,n.jsx)("div",{className:"relative ".concat(r[t].container),children:(0,n.jsx)(s.E.div,{className:"absolute inset-0 rounded-full border-2 border-[#e5e2d9]",style:{borderTopColor:"#2c2c27"},animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}})}),(0,n.jsx)("p",{className:"mt-4 font-serif text-[#5c5c52] ".concat(r[t].text),children:"Loading"})]})},o=e=>{let{isLoading:t,variant:a="thread"}=e;return(0,n.jsx)(i.M,{children:t&&(0,n.jsx)(s.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3},className:"fixed inset-0 z-[200] flex items-center justify-center bg-[#f8f8f5]/90 backdrop-blur-sm",children:(0,n.jsx)(r,{variant:a,size:"lg"})})})}},12381:function(e,t,a){a.d(t,{z:function(){return l}});var n=a(57437);a(2265);var i=a(37053),s=a(90535),r=a(93448);let o=(0,s.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:s,asChild:l=!1,...c}=e,d=l?i.g7:"button";return(0,n.jsx)(d,{"data-slot":"button",className:(0,r.cn)(o({variant:a,size:s,className:t})),...c})}},40279:function(e,t,a){a.d(t,{I:function(){return r}});var n=a(57437),i=a(2265),s=a(93448);let r=i.forwardRef((e,t)=>{let{className:a,type:i,...r}=e;return(0,n.jsx)("input",{type:i,"data-slot":"input",className:(0,s.cn)("border-[#e5e2d9] file:text-[#2c2c27] placeholder:text-[#8a8778] selection:bg-[#2c2c27] selection:text-[#f4f3f0] flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-[#8a8778] focus-visible:ring-[#8a8778]/50 focus-visible:ring-[3px]","aria-invalid:ring-[#ff4d4f]/20 dark:aria-invalid:ring-[#ff4d4f]/40 aria-invalid:border-[#ff4d4f]",a),ref:t,...r})});r.displayName="Input"},29658:function(e,t,a){var n=a(57437);a(2265),t.Z=e=>{let{size:t="md",color:a="#2c2c27",className:i=""}=e,s={sm:{container:"w-6 h-6",dot:"w-1 h-1"},md:{container:"w-10 h-10",dot:"w-1.5 h-1.5"},lg:{container:"w-16 h-16",dot:"w-2 h-2"}};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("style",{children:"\n        @keyframes loaderRotate {\n          0% {\n            transform: rotate(0deg);\n          }\n          100% {\n            transform: rotate(360deg);\n          }\n        }\n        \n        @keyframes loaderDot1 {\n          0%, 100% {\n            opacity: 0.2;\n          }\n          25% {\n            opacity: 1;\n          }\n        }\n        \n        @keyframes loaderDot2 {\n          0%, 100% {\n            opacity: 0.2;\n          }\n          50% {\n            opacity: 1;\n          }\n        }\n        \n        @keyframes loaderDot3 {\n          0%, 100% {\n            opacity: 0.2;\n          }\n          75% {\n            opacity: 1;\n          }\n        }\n        \n        @keyframes loaderDot4 {\n          0%, 100% {\n            opacity: 1;\n          }\n          50% {\n            opacity: 0.2;\n          }\n        }\n      "}),(0,n.jsx)("div",{className:"flex items-center justify-center ".concat(i),children:(0,n.jsxs)("div",{className:"relative ".concat(s[t].container),children:[(0,n.jsx)("div",{className:"absolute top-0 left-1/2 -translate-x-1/2 ".concat(s[t].dot," rounded-full"),style:{backgroundColor:a,animation:"loaderDot1 1.5s infinite"}}),(0,n.jsx)("div",{className:"absolute top-1/2 right-0 -translate-y-1/2 ".concat(s[t].dot," rounded-full"),style:{backgroundColor:a,animation:"loaderDot2 1.5s infinite"}}),(0,n.jsx)("div",{className:"absolute bottom-0 left-1/2 -translate-x-1/2 ".concat(s[t].dot," rounded-full"),style:{backgroundColor:a,animation:"loaderDot3 1.5s infinite"}}),(0,n.jsx)("div",{className:"absolute top-1/2 left-0 -translate-y-1/2 ".concat(s[t].dot," rounded-full"),style:{backgroundColor:a,animation:"loaderDot4 1.5s infinite"}}),(0,n.jsx)("div",{className:"absolute inset-0 rounded-full",style:{border:"2px solid ".concat(a),borderTopColor:"transparent",animation:"loaderRotate 1s linear infinite"}})]})})]})}},69140:function(e,t,a){a.d(t,{O:function(){return s}});var n=a(57437),i=a(93448);function s(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"skeleton",className:(0,i.cn)("bg-primary/10 animate-pulse rounded-md",t),...a})}},14362:function(e,t,a){a.d(t,{AuthProvider:function(){return o},a:function(){return l}});var n=a(57437),i=a(2265),s=a(86366);let r=(0,i.createContext)(void 0);function o(e){let{children:t}=e,[a,o]=(0,i.useState)(null),[l,c]=(0,i.useState)(null),[d,u]=(0,i.useState)(!0),[f,h]=(0,i.useState)(null);(0,i.useEffect)(()=>{m()},[]);let m=async()=>{u(!0);try{let e=await fetch("/api/auth/me",{credentials:"include"});if(console.log("Auth initialization response:",e.status,e.ok),e.ok){let t=await e.json();console.log("Auth initialization data:",t),t.success&&t.user?(o(t.user),c(t.token||"authenticated"),console.log("User authenticated:",t.user.email)):console.log("Auth initialization failed:",t.message)}else 401===e.status?(console.log("Auth initialization failed with 401, attempting token refresh"),await b()):console.log("Auth initialization response not ok:",e.status)}catch(e){console.error("Failed to initialize auth:",e)}finally{u(!1)}},x=async(e,t)=>{u(!0),h(null);try{let a=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"login",username:e,password:t}),credentials:"include"}),n=await a.json();if(n.success)o(n.user),c(n.token||"authenticated"),s.iK.loginSuccess(n.user,n.token||"authenticated"),s.Cc.show("Login successful!","success");else{let e=n.message||"Login failed";throw h(e),s.iK.loginError(e),s.Cc.show(e,"error"),Error(e)}}catch(t){let e=t.message||"Login failed";throw h(e),s.iK.loginError(e),s.Cc.show(e,"error"),t}finally{u(!1)}},g=async e=>{u(!0),h(null);try{let t=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"register",...e}),credentials:"include"}),a=await t.json();if(a.success)o(a.user),c(a.token||"authenticated"),s.iK.registerSuccess(a.user,a.token||"authenticated"),s.Cc.show("Registration successful!","success");else{let e=a.message||"Registration failed";throw h(e),s.iK.registerError(e),s.Cc.show(e,"error"),Error(e)}}catch(t){let e=t.message||"Registration failed";throw h(e),s.iK.registerError(e),s.Cc.show(e,"error"),t}finally{u(!1)}},p=async()=>{u(!0);try{await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"logout"}),credentials:"include"})}catch(e){console.error("Logout API call failed:",e)}o(null),c(null),h(null),s.iK.logout(),s.Cc.show("Logged out successfully","info"),u(!1)},b=async()=>{try{let e=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"refresh"}),credentials:"include"});e.ok&&(await e.json()).success&&console.log("Token refreshed successfully in AuthContext");let t=await fetch("/api/auth/me",{credentials:"include"});if(t.ok){let e=await t.json();if(e.success&&e.user){o(e.user),c(e.token||"authenticated"),console.log("Session refreshed successfully for user:",e.user.email);return}}console.log("Session refresh failed, clearing state"),o(null),c(null)}catch(e){console.error("Failed to refresh session:",e),o(null),c(null)}},v=async e=>{u(!0),h(null);try{let t=await fetch("/api/auth/update-profile",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e),credentials:"include"}),a=await t.json();if(a.success)return o(a.user),s.iK.profileUpdated(a.user),s.Cc.show("Profile updated successfully!","success"),a.user;{let e=a.message||"Profile update failed";throw h(e),s.Cc.show(e,"error"),Error(e)}}catch(t){let e=t.message||"Profile update failed";throw h(e),s.Cc.show(e,"error"),t}finally{u(!1)}};return(0,n.jsx)(r.Provider,{value:{user:a,token:l,isAuthenticated:!!a&&!!l,isLoading:d,error:f,login:x,register:g,logout:p,refreshSession:b,updateProfile:v,clearError:()=>{h(null)}},children:t})}function l(){let e=(0,i.useContext)(r);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},3697:function(e,t,a){var n=a(2265),i=a(6658);t.Z=function(e,t){let{setLoading:a,setVariant:s}=(0,i.r)();(0,n.useEffect)(()=>{a(e),t&&s(t)},[e,t,a,s])}}}]);