"use strict";exports.id=5725,exports.ids=[5725],exports.modules={53248:(t,e,a)=>{new(a(78578)).s({url:process.env.UPSTASH_REDIS_REST_URL||process.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""})},15725:(t,e,a)=>{a.a(t,async(t,r)=>{try{a.d(e,{Bk:()=>u,Dg:()=>d,Id:()=>g,ML:()=>y,Op:()=>p,Xq:()=>c,dv:()=>l,h2:()=>m,mJ:()=>v,xu:()=>h});var o=a(93690);a(53248);var s=t([o]);o=(s.then?(await s)():s)[0];let f={storeUrl:"https://maroon-lapwing-781450.hostingersite.com",graphqlUrl:process.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql",apiVersion:"v1"},I=process.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql",b=new o.GraphQLClient(I,{headers:{"Content-Type":"application/json",Accept:"application/json"}}),$=(0,o.gql)`
  fragment ProductFields on Product {
    id
    databaseId
    name
    slug
    description
    shortDescription
    type
    image {
      sourceUrl
      altText
    }
    galleryImages {
      nodes {
        sourceUrl
        altText
      }
    }
    ... on SimpleProduct {
      price
      regularPrice
      salePrice
      onSale
      stockStatus
      stockQuantity
    }
    ... on VariableProduct {
      price
      regularPrice
      salePrice
      onSale
      stockStatus
      stockQuantity
      attributes {
        nodes {
          name
          options
        }
      }
    }
  }
`,C=(0,o.gql)`
  fragment VariableProductWithVariations on VariableProduct {
    attributes {
      nodes {
        name
        options
      }
    }
    variations {
      nodes {
        id
        databaseId
        name
        price
        regularPrice
        salePrice
        stockStatus
        stockQuantity
        attributes {
          nodes {
            name
            value
          }
        }
      }
    }
  }
`;async function n(t,e={},a=[],r=60){try{{let o={method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:t,variables:e}),next:{}};a&&a.length>0&&(o.next.tags=a),void 0!==r&&(o.next.revalidate=r);let s=await fetch(f.graphqlUrl,o);if(!s.ok)throw Error(`WooCommerce GraphQL API responded with status ${s.status}`);let{data:n,errors:i}=await s.json();if(i)throw console.error("GraphQL Errors:",i),Error(i[0].message);return n}}catch(t){throw console.error("Error fetching from WooCommerce:",t),t}}async function i({query:t,variables:e},a=3,r=1e3){let o=0,s=null;for(;o<a;)try{return await n(t,e,[],0)}catch(t){s=t,++o<a&&(console.log(`Retrying request (${o}/${a}) after ${r}ms`),await new Promise(t=>setTimeout(t,r)),r*=2)}throw console.error(`Failed after ${a} attempts:`,s),s}(0,o.gql)`
  query GetProducts(
    $first: Int
    $after: String
    $where: RootQueryToProductConnectionWhereArgs
  ) {
    products(first: $first, after: $after, where: $where) {
      pageInfo {
        hasNextPage
        endCursor
      }
      nodes {
        ...ProductFields
        ... on VariableProduct {
          ...VariableProductWithVariations
        }
      }
    }
  }
  ${$}
  ${C}
`,(0,o.gql)`
  query GetProductBySlug($slug: ID!) {
    product(id: $slug, idType: SLUG) {
      ...ProductFields
      ... on VariableProduct {
        ...VariableProductWithVariations
      }
    }
  }
  ${$}
  ${C}
`,(0,o.gql)`
  query GetProductBySlugWithTags($slug: ID!) {
    product(id: $slug, idType: SLUG) {
      ...ProductFields
      ... on VariableProduct {
        ...VariableProductWithVariations
      }
      productTags {
        nodes {
          id
          name
          slug
        }
      }
      productCategories {
        nodes {
          id
          name
          slug
        }
      }
    }
  }
  ${$}
  ${C}
`,(0,o.gql)`
  query GetCategories(
    $first: Int
    $after: String
    $where: RootQueryToProductCategoryConnectionWhereArgs
  ) {
    productCategories(first: $first, after: $after, where: $where) {
      pageInfo {
        hasNextPage
        endCursor
      }
      nodes {
        id
        databaseId
        name
        slug
        description
        count
        image {
          sourceUrl
          altText
        }
      }
    }
  }
`;let T=(0,o.gql)`
  query GetAllProducts($first: Int = 20) {
    products(first: $first) {
      nodes {
        id
        databaseId
        name
        slug
        description
        shortDescription
        productCategories {
          nodes {
            id
            name
            slug
          }
        }
        ... on SimpleProduct {
          price
          regularPrice
          salePrice
          onSale
          stockStatus
          stockQuantity
        }
        ... on VariableProduct {
          price
          regularPrice
          salePrice
          onSale
          stockStatus
          variations {
            nodes {
              stockStatus
              stockQuantity
            }
          }
        }
        image {
          id
          sourceUrl
          altText
        }
        galleryImages {
          nodes {
            id
            sourceUrl
            altText
          }
        }
        ... on VariableProduct {
          attributes {
            nodes {
              name
              options
            }
          }
        }
        ... on SimpleProduct {
          attributes {
            nodes {
              name
              options
            }
          }
        }
      }
    }
  }
`;(0,o.gql)`
  query GetProductsByCategory($slug: ID!, $first: Int = 20) {
    productCategory(id: $slug, idType: SLUG) {
      id
      name
      slug
      description
      products(first: $first) {
        nodes {
          id
          databaseId
          name
          slug
          description
          shortDescription
          productCategories {
            nodes {
              id
              name
              slug
            }
          }
          ... on SimpleProduct {
            price
            regularPrice
            salePrice
            onSale
            stockStatus
            stockQuantity
          }
          ... on VariableProduct {
            price
            regularPrice
            salePrice
            onSale
            stockStatus
            variations {
              nodes {
                stockStatus
                stockQuantity
              }
            }
          }
          image {
            id
            sourceUrl
            altText
          }
          galleryImages {
            nodes {
              id
              sourceUrl
              altText
            }
          }
          ... on VariableProduct {
            attributes {
              nodes {
                name
                options
              }
            }
          }
          ... on SimpleProduct {
            attributes {
              nodes {
                name
                options
              }
            }
          }
        }
      }
    }
  }
`,(0,o.gql)`
  query GetAllCategories($first: Int = 20) {
    productCategories(first: $first) {
      nodes {
        id
        databaseId
        name
        slug
        description
        count
        image {
          sourceUrl
          altText
        }
        children {
          nodes {
            id
            name
            slug
          }
        }
      }
    }
  }
`,(0,o.gql)`
  query GetCart {
    cart {
      contents {
        nodes {
          key
          product {
            node {
              id
              databaseId
              name
              slug
              type
              image {
                sourceUrl
                altText
              }
            }
          }
          variation {
            node {
              id
              databaseId
              name
              attributes {
                nodes {
                  name
                  value
                }
              }
            }
          }
          quantity
          total
        }
      }
      subtotal
      total
      totalTax
      isEmpty
    }
  }
`,(0,o.gql)`
  mutation LoginUser($username: String!, $password: String!) {
    login(input: {
      clientMutationId: "login"
      username: $username
      password: $password
    }) {
      authToken
      refreshToken
      user {
        id
        databaseId
        email
        firstName
        lastName
        nicename
        nickname
        username
      }
    }
  }
`;let P=(0,o.gql)`
  query GetCart {
    cart {
      contents {
        nodes {
          key
          product {
            node {
              id
              databaseId
              name
              slug
              type
              image {
                sourceUrl
                altText
              }
            }
          }
          variation {
            node {
              id
              databaseId
              name
              attributes {
                nodes {
                  name
                  value
                }
              }
            }
          }
          quantity
          total
        }
      }
      subtotal
      total
      totalTax
      isEmpty
      contentsCount
    }
  }
`,q=(0,o.gql)`
  mutation AddToCart($productId: Int!, $variationId: Int, $quantity: Int, $extraData: String) {
    addToCart(
      input: {
        clientMutationId: "addToCart"
        productId: $productId
        variationId: $variationId
        quantity: $quantity
        extraData: $extraData
      }
    ) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                id
                databaseId
                name
                slug
                type
                image {
                  sourceUrl
                  altText
                }
              }
            }
            variation {
              node {
                id
                databaseId
                name
                attributes {
                  nodes {
                    name
                    value
                  }
                }
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
        totalTax
        isEmpty
        contentsCount
      }
    }
  }
`,S=(0,o.gql)`
  mutation RemoveItemsFromCart($keys: [ID]!, $all: Boolean) {
    removeItemsFromCart(input: { keys: $keys, all: $all }) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                id
                databaseId
                name
                slug
                type
                image {
                  sourceUrl
                  altText
                }
              }
            }
            variation {
              node {
                id
                databaseId
                name
                attributes {
                  nodes {
                    name
                    value
                  }
                }
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
        totalTax
        isEmpty
      }
    }
  }
`;async function d(t=20){try{let e=await i({query:T,variables:{first:t}});return e?.products?.nodes||[]}catch(t){return console.error("Error fetching all products:",t),[]}}async function u(t=[]){try{if(0===t.length)return{contents:{nodes:[]},subtotal:"0",total:"0",totalTax:"0",isEmpty:!0,contentsCount:0};let e=t[0],a=await c("",[e]);if(t.length>1){for(let e=1;e<t.length;e++)await c("",[t[e]]);return await l()}return a}catch(t){throw console.error("Error creating cart:",t),t}}async function l(){try{let t=await i({query:P,variables:{}});return t?.cart||null}catch(t){return console.error("Error fetching cart:",t),null}}async function c(t,e){try{if(0===e.length)throw Error("No items provided to add to cart");let t=e[0],a={productId:parseInt(t.productId),quantity:t.quantity||1,variationId:t.variationId?parseInt(t.variationId):null,extraData:null};console.log("Adding to cart with variables:",a);let r=await i({query:q,variables:a});return console.log("Add to cart response:",r),r.addToCart.cart}catch(t){throw console.error("Error adding items to cart:",t),t}}async function m(t,e){try{let t=await i({query:S,variables:{keys:e,all:!1}});return t?.removeItemsFromCart?.cart||null}catch(t){throw console.error("Error removing items from cart:",t),t}}function p(t){if(!t)return null;let e=!!t.variations?.nodes?.length,a={minVariantPrice:{amount:t.price||"0",currencyCode:"INR"},maxVariantPrice:{amount:t.price||"0",currencyCode:"INR"}};if(e&&t.variations?.nodes?.length>0){let e=t.variations.nodes.map(t=>parseFloat(t.price||"0")).filter(t=>!isNaN(t));e.length>0&&(a={minVariantPrice:{amount:String(Math.min(...e)),currencyCode:"INR"},maxVariantPrice:{amount:String(Math.max(...e)),currencyCode:"INR"}})}let r=function(t){let e=[];return t.image&&e.push({url:t.image.sourceUrl,altText:t.image.altText||t.name||""}),t.galleryImages?.nodes?.length&&t.galleryImages.nodes.forEach(a=>{t.image&&a.sourceUrl===t.image.sourceUrl||e.push({url:a.sourceUrl,altText:a.altText||t.name||""})}),e}(t),o=t.variations?.nodes?.map(t=>({id:t.id,title:t.name,price:{amount:t.price||"0",currencyCode:"USD"},availableForSale:"IN_STOCK"===t.stockStatus,selectedOptions:t.attributes?.nodes?.map(t=>({name:t.name,value:t.value}))||[],sku:t.sku||"",image:t.image?{url:t.image.sourceUrl,altText:t.image.altText||""}:null}))||[],s=t.attributes?.nodes?.map(t=>({name:t.name,values:t.options||[]}))||[],n=t.productCategories?.nodes?.map(t=>({handle:t.slug,title:t.name}))||[],i={};return t.metafields&&t.metafields.forEach(t=>{i[t.key]=t.value}),{id:t.id,handle:t.slug,title:t.name,description:t.description||"",descriptionHtml:t.description||"",priceRange:a,options:s,variants:o,images:r,collections:n,availableForSale:"OUT_OF_STOCK"!==t.stockStatus,metafields:i,_originalWooProduct:t}}(0,o.gql)`
  query GetShippingMethods {
    shippingMethods {
      nodes {
        id
        title
        description
        cost
      }
    }
  }
`,(0,o.gql)`
  query GetPaymentGateways {
    paymentGateways {
      nodes {
        id
        title
        description
        enabled
      }
    }
  }
`;let v=(t,e,a,r="")=>{if(!t||!t.metafields)return r;if(a){let o=`${a}:${e}`;return t.metafields[o]||r}return t.metafields[e]||r};function g(t){if(!t)return null;let e=t.contents?.nodes?.map(t=>{let e=t.product?.node,a=t.variation?.node;return{id:t.key,quantity:t.quantity,merchandise:{id:a?.id||e?.id,title:a?.name||e?.name,product:{id:e?.id,handle:e?.slug,title:e?.name,image:e?.image?{url:e?.image.sourceUrl,altText:e?.image.altText||""}:null},selectedOptions:a?.attributes?.nodes?.map(t=>({name:t.name,value:t.value}))||[]},cost:{totalAmount:{amount:t.total||"0",currencyCode:"USD"}}}})||[],a=t.appliedCoupons?.nodes?.map(t=>({code:t.code,amount:t.discountAmount||"0"}))||[],r=e.reduce((t,e)=>t+e.quantity,0);return{id:t.id,checkoutUrl:"",totalQuantity:r,cost:{subtotalAmount:{amount:t.subtotal||"0",currencyCode:"USD"},totalAmount:{amount:t.total||"0",currencyCode:"USD"}},lines:e,discountCodes:a}}function y(t,e=!1){let a=`${f.storeUrl}/checkout`,r=t?`?cart=${t}`:"",o="";return e||(o=`${r?"&":"?"}guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1`),`${a}${r}${o}`}(0,o.gql)`
  mutation CreateCustomer($input: RegisterCustomerInput!) {
    registerCustomer(input: $input) {
      customer {
        id
        databaseId
        email
        firstName
        lastName
        displayName
      }
      authToken
      refreshToken
    }
  }
`,(0,o.gql)`
  mutation UpdateCustomer($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      clientMutationId
      customer {
        id
        databaseId
        email
        firstName
        lastName
        displayName
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
      customerUserErrors {
        field
        message
      }
    }
  }
`,(0,o.gql)`
  query GetCustomer {
    customer {
      id
      databaseId
      email
      firstName
      lastName
      displayName
      username
      role
      date
      modified
      isPayingCustomer
      orderCount
      totalSpent
      billing {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
        email
        phone
      }
      shipping {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
      }
      orders(first: 50) {
        nodes {
          id
          databaseId
          date
          status
          total
          subtotal
          totalTax
          shippingTotal
          discountTotal
          paymentMethodTitle
          customerNote
          billing {
            firstName
            lastName
            company
            address1
            address2
            city
            state
            postcode
            country
            email
            phone
          }
          shipping {
            firstName
            lastName
            company
            address1
            address2
            city
            state
            postcode
            country
          }
          lineItems {
            nodes {
              product {
                node {
                  id
                  name
                  slug
                  image {
                    sourceUrl
                    altText
                  }
                }
              }
              variation {
                node {
                  id
                  name
                  attributes {
                    nodes {
                      name
                      value
                    }
                  }
                }
              }
              quantity
              total
              subtotal
              totalTax
            }
          }
          shippingLines {
            nodes {
              methodTitle
              total
            }
          }
          feeLines {
            nodes {
              name
              total
            }
          }
          couponLines {
            nodes {
              code
              discount
            }
          }
        }
      }
      downloadableItems {
        nodes {
          name
          downloadId
          downloadsRemaining
          accessExpires
          product {
            node {
              id
              name
            }
          }
        }
      }
      metaData {
        key
        value
      }
    }
  }
`,(0,o.gql)`
  mutation CreateAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`,(0,o.gql)`
  mutation UpdateAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`,(0,o.gql)`
  mutation DeleteAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`,(0,o.gql)`
  mutation SetDefaultAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`;let N=(0,o.gql)`
  mutation UpdateCart($input: UpdateItemQuantitiesInput!) {
    updateItemQuantities(input: $input) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                id
                name
                price
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
        totalTax
        isEmpty
      }
    }
  }
`;async function h(t){try{return(await i({query:N,variables:{input:{items:t}}})).updateItemQuantities.cart}catch(t){throw console.error("Error updating cart:",t),t}}r()}catch(t){r(t)}})}};