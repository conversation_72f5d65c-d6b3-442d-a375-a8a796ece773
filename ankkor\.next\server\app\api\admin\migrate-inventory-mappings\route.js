"use strict";(()=>{var e={};e.id=1242,e.ids=[1242],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},92048:e=>{e.exports=require("fs")},55315:e=>{e.exports=require("path")},93813:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>v,patchFetch:()=>T,requestAsyncStorage:()=>S,routeModule:()=>E,serverHooks:()=>f,staticGenerationAsyncStorage:()=>y});var n={};t.r(n),t.d(n,{POST:()=>R});var s=t(49303),o=t(88716),i=t(60670),a=t(87070),c=t(92048),p=t(55315),u=t.n(p);let l=new(t(94868)).s({url:process.env.UPSTASH_REDIS_REST_URL||process.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""}),_={};async function m(e){try{if(!(process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_URL&&process.env.NEXT_PUBLIC_KV_REST_API_TOKEN)){for(let{inventoryItemId:r,productHandle:t}of e)_[r]=t;console.log(`Updated ${e.length} inventory mappings in memory`)}else{let r=l.pipeline();for(let{inventoryItemId:t,productHandle:n}of e)r.set(`inventory:mapping:${t}`,n);await r.exec(),console.log(`Updated ${e.length} inventory mappings in Redis`)}return!0}catch(r){console.error("Error updating inventory mappings in Redis:",r);try{for(let{inventoryItemId:r,productHandle:t}of e)_[r]=t;return console.log(`Updated ${e.length} inventory mappings in memory fallback`),!0}catch(e){return console.error("Error updating in memory fallback:",e),!1}}}var g=t(79534);let d=u().join(process.cwd(),".inventory-map.json");async function R(e){try{if(!(0,g.Pc)())return a.NextResponse.json({success:!1,error:"Redis is not available. Check your environment variables."},{status:500});let e=!1;try{await c.promises.access(d),e=!0}catch(r){e=!1}if(!e)return a.NextResponse.json({success:!1,error:"Inventory mapping file does not exist. Nothing to migrate."},{status:404});let r=await c.promises.readFile(d,"utf-8"),t=JSON.parse(r);if(0===Object.keys(t).length)return a.NextResponse.json({success:!0,message:"No mappings found in file. Nothing to migrate."},{status:200});let n=Object.entries(t).map(([e,r])=>({inventoryItemId:e,productHandle:r}));if(!await m(n))return a.NextResponse.json({success:!1,error:"Failed to update Redis with inventory mappings"},{status:500});{let e=`${d}.backup-${Date.now()}`;return await c.promises.rename(d,e),a.NextResponse.json({success:!0,message:`Successfully migrated ${n.length} inventory mappings to Redis`,migrated:n.length,backupPath:e})}}catch(e){return console.error("Error migrating inventory mappings:",e),a.NextResponse.json({success:!1,error:"Migration error",message:e.message},{status:500})}}let E=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/admin/migrate-inventory-mappings/route",pathname:"/api/admin/migrate-inventory-mappings",filename:"route",bundlePath:"app/api/admin/migrate-inventory-mappings/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\admin\\migrate-inventory-mappings\\route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:S,staticGenerationAsyncStorage:y,serverHooks:f}=E,v="/api/admin/migrate-inventory-mappings/route";function T(){return(0,i.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:y})}},79534:(e,r,t)=>{t.d(r,{Fs:()=>p,IV:()=>c,Pc:()=>o,U2:()=>i,mJ:()=>s,t8:()=>a});let n=new(t(94868)).s({url:process.env.UPSTASH_REDIS_REST_URL||process.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""}),s={SHORT:300,MEDIUM:3600,LONG:86400,WEEK:604800};function o(){return!!(process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_URL&&process.env.NEXT_PUBLIC_KV_REST_API_TOKEN)}async function i(e){if(!o())return null;try{return await n.get(e)||null}catch(r){return console.error(`Redis get error for key ${e}:`,r),null}}async function a(e,r,t){if(!o())return!1;try{return t?await n.setex(e,t,r):await n.set(e,r),!0}catch(r){return console.error(`Redis set error for key ${e}:`,r),!1}}async function c(e){if(!o())return!1;try{return await n.del(e),!0}catch(r){return console.error(`Redis delete error for key ${e}:`,r),!1}}async function p(e,r,t=s.MEDIUM){if(!o())return await r();try{let s=await n.get(e);if(null!==s)return console.log(`Cache hit for key: ${e}`),JSON.parse(s);console.log(`Cache miss for key: ${e}, fetching fresh data`);let o=await r();return await n.setex(e,t,JSON.stringify(o)),o}catch(t){return console.error(`Redis cache error for key ${e}:`,t),await r()}}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[8948,5972,4766,4868],()=>t(93813));module.exports=n})();