(()=>{var e={};e.id=9153,e.ids=[9153],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},33897:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.Z,__next_app__:()=>u,originalPathname:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>l}),r(5818),r(11360),r(7629),r(11930),r(12523);var n=r(23191),o=r(88716),i=r(43315),a=r(95231),s={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>a[e]);r.d(t,s);let l=["",{children:["collection",{children:["shirts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5818)),"E:\\ankkorwoo\\ankkor\\src\\app\\collection\\shirts\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,11360)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,7629)),"E:\\ankkorwoo\\ankkor\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,11930)),"E:\\ankkorwoo\\ankkor\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],c=["E:\\ankkorwoo\\ankkor\\src\\app\\collection\\shirts\\page.tsx"],d="/collection/shirts/page",u={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/collection/shirts/page",pathname:"/collection/shirts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},37973:(e,t,r)=>{Promise.resolve().then(r.bind(r,41383))},46226:(e,t,r)=>{"use strict";r.d(t,{default:()=>o.a});var n=r(69029),o=r.n(n)},69029:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return s}});let n=r(91174),o=r(23078),i=r(92481),a=n._(r(86820));function s(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=i.Image},41383:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{default:()=>g});var o=r(10326),i=r(17577),a=r(46226),s=r(92148),l=r(53471),c=r(38227),d=r(9512),u=r(15725),p=r(45107),m=e([l,u]);function g(){let[e,t]=(0,i.useState)([]),[r,n]=(0,i.useState)(!0),[m,g]=(0,i.useState)(null),[h,x]=(0,i.useState)(null);(0,d.Z)(r,"fabric");let f=[...e].sort((e,t)=>{let r=e._originalWooProduct?.dateCreated||e._originalWooProduct?.date_created||e.id,n=t._originalWooProduct?.dateCreated||t._originalWooProduct?.date_created||t.id;return r&&n&&r!==e.id&&n!==t.id?new Date(n).getTime()-new Date(r).getTime():t.id.localeCompare(e.id)}),y={initial:{opacity:0,y:20},animate:{opacity:1,y:0,transition:{duration:.5}},exit:{opacity:0,y:20,transition:{duration:.3}}};return(0,o.jsxs)("div",{className:"min-h-screen bg-[#f8f8f5] pt-8 pb-24",children:[o.jsx("div",{className:"container mx-auto px-4 mb-12",children:(0,o.jsxs)("div",{className:"text-center max-w-3xl mx-auto",children:[o.jsx("h1",{className:"text-4xl font-serif font-bold mb-4 text-[#2c2c27]",children:"Shirts Collection"}),o.jsx("p",{className:"text-[#5c5c52] mb-8",children:"Discover our meticulously crafted shirts, designed with premium fabrics and impeccable attention to detail."})]})}),(0,o.jsxs)("div",{className:"relative h-[300px] mb-16 overflow-hidden",children:[o.jsx(a.default,{src:"https://images.unsplash.com/photo-1552374196-1ab2a1c593e8?q=80",alt:"Ankkor Shirts Collection",fill:!0,sizes:"(max-width: 768px) 100vw, 50vw",className:"object-cover image-animate"}),o.jsx("div",{className:"absolute inset-0 bg-[#2c2c27] bg-opacity-30 flex items-center justify-center",children:(0,o.jsxs)("div",{className:"text-center text-white",children:[o.jsx("h2",{className:"text-3xl font-serif font-bold mb-4",children:"Signature Shirts"}),o.jsx("p",{className:"text-lg max-w-xl mx-auto",children:"Impeccably tailored for the perfect fit"})]})})]}),(0,o.jsxs)("div",{className:"container mx-auto px-4",children:[m&&!r&&o.jsx("div",{className:"text-center py-16",children:(0,o.jsxs)("div",{className:"max-w-md mx-auto",children:[o.jsx("h3",{className:"text-xl font-serif text-[#2c2c27] mb-4",children:"Service Temporarily Unavailable"}),o.jsx("p",{className:"text-[#5c5c52] mb-6",children:m}),o.jsx("button",{onClick:()=>window.location.reload(),className:"bg-[#2c2c27] text-white px-6 py-2 hover:bg-[#3d3d35] transition-colors",children:"Try Again"})]})}),r&&o.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8",children:Array.from({length:9}).map((e,t)=>(0,o.jsxs)("div",{className:"animate-pulse",children:[o.jsx(c.O,{className:"w-full h-80 mb-4"}),o.jsx(c.O,{className:"w-3/4 h-4 mb-2"}),o.jsx(c.O,{className:"w-1/2 h-4"})]},t))}),o.jsx("div",{className:"flex justify-end items-center mb-8",children:(0,o.jsxs)("div",{className:"text-[#5c5c52] text-sm",children:[f.length," products"]})}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("div",{className:"hidden md:flex justify-between items-center mb-8",children:[o.jsx("h2",{className:"text-[#2c2c27] font-serif text-xl",children:"Shirts Collection"}),(0,o.jsxs)("div",{className:"text-[#5c5c52]",children:[f.length," products"]})]}),!r&&!m&&f.length>0&&o.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8",children:f.map(e=>{let t="",r=!1;try{if(e.variants&&e.variants.length>0){let n=e.variants[0];if(n&&n.id){if(t=n.id,r=!0,!t.startsWith("gid://shopify/ProductVariant/")){let n=t.replace(/\D/g,"");n?t=`gid://shopify/ProductVariant/${n}`:(console.warn(`Cannot parse variant ID for product ${e.title}: ${t}`),r=!1)}console.log(`Product ${e.title} using variant ID: ${t}`)}}if(!r&&e.id&&e.id.includes("/")){let n=e.id.split("/"),o=n[n.length-1];o&&/^\d+$/.test(o)&&(t=`gid://shopify/ProductVariant/${o}`,console.warn(`Using fallback variant ID for ${e.title}: ${t}`),r=!0)}}catch(t){console.error(`Error processing variant for product ${e.title}:`,t),r=!1}return r||console.error(`No valid variant ID found for product: ${e.title}`),o.jsx(s.E.div,{variants:y,initial:"initial",animate:"animate",exit:"exit",layout:!0,children:o.jsx(l.Z,{id:e.id,name:e.title,slug:e.handle,price:e._originalWooProduct?.salePrice||e._originalWooProduct?.price||e.priceRange?.minVariantPrice?.amount||"0",image:e.images[0]?.url||"",material:(0,u.mJ)(e,"custom_material",void 0,"Premium Fabric"),isNew:!0,stockStatus:e._originalWooProduct?.stockStatus||"IN_STOCK",compareAtPrice:e.compareAtPrice,regularPrice:e._originalWooProduct?.regularPrice,salePrice:e._originalWooProduct?.salePrice,onSale:e._originalWooProduct?.onSale||!1,currencySymbol:(0,p.jK)(e.currencyCode),currencyCode:e.currencyCode||"INR",shortDescription:e._originalWooProduct?.shortDescription,type:e._originalWooProduct?.type})},e.id)})}),!r&&!m&&0===f.length&&o.jsx("div",{className:"text-center py-16",children:o.jsx("p",{className:"text-[#5c5c52]",children:"No shirts available at the moment."})})]})]})]})}[l,u]=m.then?(await m)():m,n()}catch(e){n(e)}})},38227:(e,t,r)=>{"use strict";r.d(t,{O:()=>i});var n=r(10326),o=r(51223);function i({className:e,...t}){return n.jsx("div",{"data-slot":"skeleton",className:(0,o.cn)("bg-primary/10 animate-pulse rounded-md",e),...t})}},9512:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o}),r(17577);var n=r(2861);let o=function(e,t){let{setLoading:r,setVariant:o}=(0,n.r)()}},92079:(e,t,r)=>{"use strict";r.r(t),r.d(t,{addInventoryMapping:()=>d,clearInventoryMappings:()=>g,getAllInventoryMappings:()=>m,getProductHandleFromInventory:()=>u,loadInventoryMap:()=>l,saveInventoryMap:()=>c,updateInventoryMappings:()=>p});var n=r(78578);let o="inventory:mapping:",i=new n.s({url:process.env.UPSTASH_REDIS_REST_URL||process.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""}),a={};function s(){return!!(process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_URL&&process.env.NEXT_PUBLIC_KV_REST_API_TOKEN)}async function l(){if(!s())return{...a};try{let e=await i.keys(`${o}*`);if(0===e.length)return console.log("No existing inventory mappings found in Redis"),{};let t={},r=await i.mget(...e);return e.forEach((e,n)=>{let i=e.replace(o,""),a=r[n];t[i]=a}),console.log(`Loaded inventory mapping with ${Object.keys(t).length} entries from Redis`),t}catch(e){return console.error("Error loading inventory mapping from Redis:",e),console.log("Falling back to in-memory storage"),{...a}}}async function c(e){if(s())try{let t=i.pipeline(),r=await i.keys(`${o}*`);r.length>0&&t.del(...r),Object.entries(e).forEach(([e,r])=>{t.set(`${o}${e}`,r)}),await t.exec(),console.log(`Saved inventory mapping with ${Object.keys(e).length} entries to Redis`)}catch(t){console.error("Error saving inventory mapping to Redis:",t),console.log("Falling back to in-memory storage"),Object.assign(a,e)}else Object.assign(a,e),console.log(`Saved inventory mapping with ${Object.keys(e).length} entries to memory`)}async function d(e,t){try{return s()?(await i.set(`${o}${e}`,t),console.log(`Added mapping to Redis: ${e} -> ${t}`)):(a[e]=t,console.log(`Added mapping to memory: ${e} -> ${t}`)),!0}catch(r){console.error("Error adding inventory mapping:",r);try{return a[e]=t,console.log(`Added mapping to memory fallback: ${e} -> ${t}`),!0}catch(e){return console.error("Error adding to memory fallback:",e),!1}}}async function u(e){try{if(s())return await i.get(`${o}${e}`)||null;return a[e]||null}catch(t){console.error("Error getting product handle from Redis:",t);try{return a[e]||null}catch(e){return console.error("Error getting from memory fallback:",e),null}}}async function p(e){try{if(s()){let t=i.pipeline();for(let{inventoryItemId:r,productHandle:n}of e)t.set(`${o}${r}`,n);await t.exec(),console.log(`Updated ${e.length} inventory mappings in Redis`)}else{for(let{inventoryItemId:t,productHandle:r}of e)a[t]=r;console.log(`Updated ${e.length} inventory mappings in memory`)}return!0}catch(t){console.error("Error updating inventory mappings in Redis:",t);try{for(let{inventoryItemId:t,productHandle:r}of e)a[t]=r;return console.log(`Updated ${e.length} inventory mappings in memory fallback`),!0}catch(e){return console.error("Error updating in memory fallback:",e),!1}}}async function m(){return await l()}async function g(){try{if(s()){let e=await i.keys(`${o}*`);e.length>0&&await i.del(...e),console.log("Cleared all inventory mappings from Redis")}else Object.keys(a).forEach(e=>{delete a[e]}),console.log("Cleared all inventory mappings from memory");return!0}catch(e){return console.error("Error clearing inventory mappings:",e),!1}}},45107:(e,t,r)=>{"use strict";function n(e="INR"){switch(e){case"INR":return"₹";case"USD":return"$";case"EUR":return"€";case"GBP":return"\xa3";default:return e}}r.d(t,{jK:()=>n}),r(92079)},51223:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var n=r(41135),o=r(31009);function i(...e){return(0,o.m6)((0,n.W)(e))}},5818:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\collection\shirts\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8948,1057,2481,325,8578,5436,5725,5916,3471],()=>r(33897));module.exports=n})();