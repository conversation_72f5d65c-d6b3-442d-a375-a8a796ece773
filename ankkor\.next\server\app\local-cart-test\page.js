(()=>{var e={};e.id=5046,e.ids=[5046],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},77585:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.Z,__next_app__:()=>u,originalPathname:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>o}),r(27854),r(11360),r(7629),r(11930),r(12523);var s=r(23191),n=r(88716),a=r(43315),i=r(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let o=["",{children:["local-cart-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,27854)),"E:\\ankkorwoo\\ankkor\\src\\app\\local-cart-test\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,11360)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,7629)),"E:\\ankkorwoo\\ankkor\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,11930)),"E:\\ankkorwoo\\ankkor\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],c=["E:\\ankkorwoo\\ankkor\\src\\app\\local-cart-test\\page.tsx"],d="/local-cart-test/page",u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/local-cart-test/page",pathname:"/local-cart-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},59893:(e,t,r)=>{Promise.resolve().then(r.bind(r,29012))},75290:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},29012:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>u});var n=r(10326),a=r(17577),i=r(86806),l=r(72248),o=r(75290),c=r(15725),d=e([c]);function u(){let[e,t]=(0,a.useState)(!1),[r,s]=(0,a.useState)([]),[d,u]=(0,a.useState)(null),{items:p,itemCount:m,addToCart:x,updateCartItem:f,removeCartItem:h,clearCart:v,subtotal:g,total:b}=(0,i.rY)(),j=e=>{x({productId:e.databaseId.toString(),name:e.name,price:e.price||"0",quantity:1,image:{url:e.image?.sourceUrl||"",altText:e.image?.altText||e.name}})},y=(e,t)=>{f(e,t)},k=e=>{h(e)},N=async()=>{try{for(let e of(t(!0),await c.Bk(),p))await c.Xq("",[{productId:parseInt(e.productId),variationId:e.variationId?parseInt(e.variationId):void 0,quantity:e.quantity}]);window.location.href="/checkout"}catch(e){console.error("Error during checkout:",e),u(e instanceof Error?e.message:"Failed to proceed to checkout")}finally{t(!1)}};return(0,n.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[n.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Local Cart Test"}),d&&(0,n.jsxs)("div",{className:"mb-6 p-4 bg-red-50 text-red-700 rounded-md",children:[d,n.jsx(l.z,{variant:"outline",size:"sm",className:"ml-4",onClick:()=>u(null),children:"Dismiss"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,n.jsxs)("div",{className:"border rounded-md p-4",children:[n.jsx("h2",{className:"text-lg font-medium mb-4",children:"Products"}),e&&n.jsx("div",{className:"flex items-center justify-center py-8",children:n.jsx(o.Z,{className:"h-8 w-8 animate-spin text-gray-400"})}),!e&&0===r.length&&n.jsx("p",{className:"text-gray-500",children:"No products available"}),n.jsx("ul",{className:"space-y-4",children:r.slice(0,5).map(e=>(0,n.jsxs)("li",{className:"flex justify-between items-center border-b pb-2",children:[(0,n.jsxs)("div",{children:[n.jsx("h3",{className:"font-medium",children:e.name}),(0,n.jsxs)("p",{className:"text-sm text-gray-500",children:["₹",e.price||"0"]})]}),n.jsx(l.z,{onClick:()=>j(e),size:"sm",children:"Add to Cart"})]},e.id))})]}),(0,n.jsxs)("div",{className:"border rounded-md p-4",children:[(0,n.jsxs)("h2",{className:"text-lg font-medium mb-4",children:["Cart (",m," items)"]}),0===p.length?n.jsx("p",{className:"text-gray-500",children:"Your cart is empty"}):(0,n.jsxs)(n.Fragment,{children:[n.jsx("ul",{className:"space-y-4 mb-4",children:p.map(e=>(0,n.jsxs)("li",{className:"flex justify-between items-center border-b pb-2",children:[(0,n.jsxs)("div",{children:[n.jsx("h3",{className:"font-medium",children:e.name}),(0,n.jsxs)("p",{className:"text-sm text-gray-500",children:["₹",parseFloat(e.price).toFixed(2)," \xd7 ",e.quantity]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[n.jsx(l.z,{variant:"outline",size:"sm",onClick:()=>y(e.id,e.quantity-1),disabled:e.quantity<=1,children:"-"}),n.jsx("span",{className:"w-8 text-center",children:e.quantity}),n.jsx(l.z,{variant:"outline",size:"sm",onClick:()=>y(e.id,e.quantity+1),children:"+"}),n.jsx(l.z,{variant:"ghost",size:"sm",onClick:()=>k(e.id),children:"\xd7"})]})]},e.id))}),(0,n.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,n.jsxs)("div",{className:"flex justify-between",children:[n.jsx("span",{children:"Subtotal:"}),(0,n.jsxs)("span",{children:["$",g().toFixed(2)]})]}),(0,n.jsxs)("div",{className:"flex justify-between font-medium",children:[n.jsx("span",{children:"Total:"}),(0,n.jsxs)("span",{children:["$",b().toFixed(2)]})]})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)(l.z,{className:"w-full",onClick:N,disabled:e,children:[e&&n.jsx(o.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Proceed to Checkout"]}),n.jsx(l.z,{variant:"outline",className:"w-full",onClick:()=>{v()},children:"Clear Cart"})]})]})]})]})]})}c=(d.then?(await d)():d)[0],s()}catch(e){s(e)}})},72248:(e,t,r)=>{"use strict";let s,n;r.d(t,{z:()=>p});var a=r(10326);r(17577);var i=r(34214),l=r(41135);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,c=l.W;var d=r(51223);let u=(s="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",n={variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}},e=>{var t;if((null==n?void 0:n.variants)==null)return c(s,null==e?void 0:e.class,null==e?void 0:e.className);let{variants:r,defaultVariants:a}=n,i=Object.keys(r).map(t=>{let s=null==e?void 0:e[t],n=null==a?void 0:a[t];if(null===s)return null;let i=o(s)||o(n);return r[t][i]}),l=e&&Object.entries(e).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return c(s,i,null==n?void 0:null===(t=n.compoundVariants)||void 0===t?void 0:t.reduce((e,t)=>{let{class:r,className:s,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...l}[t]):({...a,...l})[t]===r})?[...e,r,s]:e},[]),null==e?void 0:e.class,null==e?void 0:e.className)});function p({className:e,variant:t,size:r,asChild:s=!1,...n}){let l=s?i.g7:"button";return a.jsx(l,{"data-slot":"button",className:(0,d.cn)(u({variant:t,size:r,className:e})),...n})}},51223:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var s=r(41135),n=r(31009);function a(...e){return(0,n.m6)((0,s.W)(e))}},27854:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\local-cart-test\page.tsx#default`)},48051:(e,t,r)=>{"use strict";r.d(t,{F:()=>a,e:()=>i});var s=r(17577);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let r=!1,s=e.map(e=>{let s=n(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():n(e[t],null)}}}}function i(...e){return s.useCallback(a(...e),e)}},34214:(e,t,r)=>{"use strict";r.d(t,{g7:()=>i});var s=r(17577),n=r(48051),a=r(10326),i=s.forwardRef((e,t)=>{let{children:r,...n}=e,i=s.Children.toArray(r),o=i.find(c);if(o){let e=o.props.children,r=i.map(t=>t!==o?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,a.jsx)(l,{...n,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,r):null})}return(0,a.jsx)(l,{...n,ref:t,children:r})});i.displayName="Slot";var l=s.forwardRef((e,t)=>{let{children:r,...a}=e;if(s.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),i=function(e,t){let r={...t};for(let s in t){let n=e[s],a=t[s];/^on[A-Z]/.test(s)?n&&a?r[s]=(...e)=>{a(...e),n(...e)}:n&&(r[s]=n):"style"===s?r[s]={...n,...a}:"className"===s&&(r[s]=[n,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==s.Fragment&&(i.ref=t?(0,n.F)(t,e):e),s.cloneElement(r,i)}return s.Children.count(r)>1?s.Children.only(null):null});l.displayName="SlotClone";var o=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});function c(e){return s.isValidElement(e)&&e.type===o}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,1057,325,8578,5436,5725],()=>r(77585));module.exports=s})();