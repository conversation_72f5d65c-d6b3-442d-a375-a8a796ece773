"use strict";(()=>{var e={};e.id=3703,e.ids=[3703],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},80685:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>x,patchFetch:()=>h,requestAsyncStorage:()=>l,routeModule:()=>d,serverHooks:()=>m,staticGenerationAsyncStorage:()=>c});var o={};r.r(o),r.d(o,{GET:()=>p});var a=r(49303),s=r(88716),n=r(60670),i=r(55661),u=r(60707);async function p(){let e=await {rules:{userAgent:"*",allow:"/",disallow:["/api/","/admin/","/account/","/cart/","/404","/500"]},sitemap:"https://www.ankkor.com/sitemap.xml"},t=(0,u.resolveRouteData)(e,"robots");return new i.NextResponse(t,{headers:{"Content-Type":"text/plain","Cache-Control":"public, max-age=0, must-revalidate"}})}let d=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/robots.txt/route",pathname:"/robots.txt",filename:"robots",bundlePath:"app/robots.txt/route"},resolvedPagePath:"next-metadata-route-loader?page=%2Frobots.txt%2Froute&filePath=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp%5Crobots.ts&isDynamic=1!?__next_metadata_route__",nextConfigOutput:"",userland:o}),{requestAsyncStorage:l,staticGenerationAsyncStorage:c,serverHooks:m}=d,x="/robots.txt/route";function h(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:c})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,1346],()=>r(80685));module.exports=o})();