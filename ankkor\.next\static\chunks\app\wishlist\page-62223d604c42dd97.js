(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4456],{49759:function(e,s,t){Promise.resolve().then(t.bind(t,84625))},84625:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return y}});var a=t(57437),i=t(2265),r=t(27648),c=t(33145),l=t(88997),n=t(32489),d=t(18930),o=t(42449),m=t(30401),h=t(92371),x=t(87758),u=t(3371),f=t(29658),p=t(43886),j=t(11738),v=t(12381);let g=e=>{if("number"==typeof e)return e;if(!e)return 0;let s=parseFloat(e.toString().replace(/[^\d.-]/g,""));return isNaN(s)?0:s},N=e=>g(e).toFixed(2);function y(){let e=(0,x.useLocalCartStore)(),{items:s,removeFromWishlist:t,clearWishlist:g}=(0,h.useWishlistStore)(),{isAuthenticated:y,isLoading:b}=(0,u.O)(),[w,C]=(0,i.useState)(!0),[P,k]=(0,i.useState)({}),[T,F]=(0,i.useState)(!1);(0,i.useEffect)(()=>{if(!y&&s.length>0&&!b&&"true"!==sessionStorage.getItem("wishlist_prompt_dismissed")){let e=setTimeout(()=>{F(!0)},3e3);return()=>clearTimeout(e)}},[y,s.length,b]),(0,i.useEffect)(()=>{if(!b){let e=setTimeout(()=>{C(!1)},500);return()=>clearTimeout(e)}},[b]);let S=async function(s){var a,i,r;let c=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{console.log("Adding item to cart: ".concat(s.name||"Unnamed Product"));let i={productId:s.id,variationId:(null===(a=s.variantId)||void 0===a?void 0:a.replace("gid://shopify/ProductVariant/",""))||void 0,quantity:1,name:s.name||"Unnamed Product",price:s.price?N(s.price):"0.00",image:s.image?{url:s.image,altText:s.name||"Product image"}:void 0};await e.addToCart(i),j.Am.success("".concat(s.name||"Product"," added to your cart!")),k(e=>({...e,[s.id]:!0})),setTimeout(()=>{k(e=>({...e,[s.id]:!1}))},2e3),c&&t(s.id)}catch(e){console.error("Error from cart.addToCart:",e),e instanceof Error?(null===(i=e.message)||void 0===i?void 0:i.includes("out of stock"))?j.Am.error("This product is currently out of stock."):(null===(r=e.message)||void 0===r?void 0:r.includes("invalid"))?j.Am.error("This product has an invalid format. Please try another item."):j.Am.error(e.message||"Unable to add this item to your cart. Please try again later."):j.Am.error("An unexpected error occurred. Please try again later.")}};return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-serif",children:"My Wishlist"}),s.length>0&&(0,a.jsx)(v.z,{variant:"outline",onClick:g,className:"text-sm",children:"Clear All"})]}),w?(0,a.jsx)("div",{className:"flex items-center justify-center py-24",children:(0,a.jsx)(f.Z,{size:"lg",color:"#8a8778"})}):(0,a.jsxs)(a.Fragment,{children:[!y&&s.length>0&&!T&&(0,a.jsx)("div",{className:"mb-6 p-3 bg-blue-50 border border-blue-200 rounded-md",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(l.Z,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,a.jsxs)("p",{className:"text-sm text-blue-800",children:["Your wishlist is saved locally on this device.",(0,a.jsx)(r.default,{href:"/sign-up",className:"ml-1 font-medium underline hover:no-underline",children:"Create an account"})," to access it from anywhere."]})]})}),!y&&T&&s.length>0&&(0,a.jsx)(p.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mb-8 p-4 border border-[#e5e2d9] bg-[#f8f8f5] rounded-md",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(l.Z,{className:"h-5 w-5 text-[#8a8778] mt-1 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-serif font-medium text-[#2c2c27]",children:"Sync your wishlist across devices"}),(0,a.jsx)("p",{className:"text-sm text-[#5c5c52] mt-1",children:"Your wishlist works without an account and is saved locally. Sign in to sync it across all your devices."})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(r.default,{href:"/sign-up",className:"text-sm text-[#2c2c27] font-medium hover:text-[#8a8778] transition-colors",children:"Sign Up"}),(0,a.jsx)("button",{onClick:()=>{F(!1),sessionStorage.setItem("wishlist_prompt_dismissed","true")},className:"text-[#8a8778] hover:text-[#2c2c27] transition-colors","aria-label":"Dismiss",children:(0,a.jsx)(n.Z,{className:"h-4 w-4"})})]})]})}),0===s.length?(0,a.jsxs)("div",{className:"text-center py-16",children:[(0,a.jsx)("div",{className:"inline-flex justify-center items-center w-16 h-16 bg-gray-100 rounded-full mb-4",children:(0,a.jsx)(l.Z,{className:"h-8 w-8 text-gray-400"})}),(0,a.jsx)("h2",{className:"text-xl font-medium mb-2",children:"Your wishlist is empty"}),(0,a.jsx)("p",{className:"text-gray-500 mb-2",children:"Add items you love to your wishlist. Review them anytime and easily move them to the cart."}),!y&&(0,a.jsx)("p",{className:"text-sm text-gray-400 mb-6",children:"No account needed - your wishlist is saved locally on this device."}),(0,a.jsx)(r.default,{href:"/categories",children:(0,a.jsx)(v.z,{children:"Continue Shopping"})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:s.map(e=>(0,a.jsxs)("div",{className:"border rounded-md overflow-hidden",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(r.default,{href:"/product/".concat(e.handle||"#"),children:(0,a.jsx)("div",{className:"aspect-square relative bg-gray-100",children:(0,a.jsx)(c.default,{src:e.image||"/placeholder-image.jpg",alt:e.name||"Product image",fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",className:"object-cover"})})}),(0,a.jsx)("button",{onClick:()=>t(e.id),className:"absolute top-2 right-2 p-2 bg-white rounded-full shadow-md hover:bg-gray-100","aria-label":"Remove from wishlist",children:(0,a.jsx)(d.Z,{className:"h-4 w-4 text-gray-600"})})]}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)(r.default,{href:"/product/".concat(e.handle||"#"),children:(0,a.jsx)("h2",{className:"font-medium text-lg hover:underline",children:e.name||"Unnamed Product"})}),(0,a.jsxs)("p",{className:"text-gray-700 my-2",children:["₹",e.price?N(e.price):"0.00"]}),(0,a.jsxs)(v.z,{onClick:()=>S(e),className:"w-full mt-2 flex items-center justify-center gap-2",children:[(0,a.jsx)(o.Z,{className:"h-4 w-4"}),"Add to Cart"]})]})]},e.id))}),(0,a.jsx)("div",{className:"mt-12 text-center",children:(0,a.jsx)(r.default,{href:"/categories",children:(0,a.jsx)(v.z,{variant:"outline",children:"Continue Shopping"})})}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full border-collapse",children:[(0,a.jsx)("thead",{className:"border-b border-[#e5e2d9]",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"py-4 text-left font-serif text-[#2c2c27]",children:"Product"}),(0,a.jsx)("th",{className:"py-4 text-left font-serif text-[#2c2c27]",children:"Price"}),(0,a.jsx)("th",{className:"py-4 text-center font-serif text-[#2c2c27]",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-[#e5e2d9]",children:s.map(e=>(0,a.jsxs)("tr",{className:"group",children:[(0,a.jsx)("td",{className:"py-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"relative mr-4 h-24 w-20 overflow-hidden bg-[#f4f3f0]",children:(0,a.jsx)(r.default,{href:"/product/".concat(e.handle||"#"),children:(0,a.jsx)(c.default,{src:e.image||"/placeholder-image.jpg",alt:e.name||"Product image",fill:!0,sizes:"(max-width: 768px) 80px, 120px",className:"object-cover object-center transition-transform duration-500 group-hover:scale-105"})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(r.default,{href:"/product/".concat(e.handle||"#"),className:"font-serif text-lg text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:e.name||"Unnamed Product"}),(0,a.jsx)("p",{className:"text-sm text-[#8a8778]",children:e.material||"Material not specified"})]})]})}),(0,a.jsxs)("td",{className:"py-6 font-medium text-[#2c2c27]",children:["₹",e.price?N(e.price):"0.00"]}),(0,a.jsx)("td",{className:"py-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,a.jsx)(p.E.button,{onClick:()=>S(e),className:"".concat(P[e.id]?"bg-[#2c2c27] text-[#f4f3f0]":"text-[#2c2c27]"," p-2 rounded-full transition-colors hover:text-[#8a8778]"),"aria-label":"Add to cart",whileTap:{scale:.95},children:P[e.id]?(0,a.jsx)(m.Z,{className:"h-5 w-5"}):(0,a.jsx)(o.Z,{className:"h-5 w-5"})}),(0,a.jsx)(p.E.button,{onClick:()=>t(e.id),className:"text-[#2c2c27] p-2 rounded-full hover:text-[#8a8778] transition-colors","aria-label":"Remove from wishlist",whileTap:{scale:.95},children:(0,a.jsx)(n.Z,{className:"h-5 w-5"})})]})})]},e.id))})]})})]})]}),(0,a.jsx)(j.x7,{position:"top-center",toastOptions:{duration:3e3,style:{background:"#F8F8F5",color:"#2C2C27",border:"1px solid #E5E2D9"},success:{iconTheme:{primary:"#2C2C27",secondary:"#F8F8F5"}},error:{iconTheme:{primary:"#2C2C27",secondary:"#F8F8F5"}}}})]})}}},function(e){e.O(0,[5540,4822,8787,8261,1539,2188,6003,7231,2323,9960,7696,8002,7111,8989,4513,8476,8496,8966,3903,6076,4596,62,6271,8726,8133,8049,2870,632,6613,9965,5270,2647,9971,9290,6459,7800,4818,1729,5526,4615,7406,2532,2068,4595,3280,8790,1104,7158,7044,9482,6628,986,4754,1744],function(){return e(e.s=49759)}),_N_E=e.O()}]);