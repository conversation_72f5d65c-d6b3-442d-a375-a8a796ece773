"use strict";(()=>{var e={};e.id=55,e.ids=[55],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},18699:(e,o,r)=>{r.r(o),r.d(o,{originalPathname:()=>h,patchFetch:()=>A,requestAsyncStorage:()=>u,routeModule:()=>p,serverHooks:()=>g,staticGenerationAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{OPTIONS:()=>c,POST:()=>l});var t=r(49303),n=r(88716),a=r(60670),i=r(87070);async function l(e){try{let o=await e.json(),r=process.env.WOOCOMMERCE_GRAPHQL_URL||"https://maroon-lapwing-781450.hostingersite.com/graphql";console.log("\uD83D\uDD17 GraphQL Proxy - Using endpoint:",r),console.log("\uD83D\uDCCA GraphQL Proxy - Request body:",JSON.stringify(o,null,2));let s=e.headers.get("origin")||"",t={"Content-Type":"application/json",Accept:"application/json"},n=e.headers.get("woocommerce-session");n&&(t["woocommerce-session"]=n);let a=e.headers.get("cookie");a&&(t.cookie=a);let l=await fetch(r,{method:"POST",headers:t,body:JSON.stringify(o),credentials:"include"}),c=await l.json();console.log("\uD83D\uDCCA GraphQL Proxy - Response status:",l.status),console.log("\uD83D\uDCCA GraphQL Proxy - Response data:",JSON.stringify(c,null,2));let p={"Access-Control-Allow-Origin":s,"Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, woocommerce-session, cookie","Access-Control-Allow-Credentials":"true",Vary:"Origin"},u=l.headers.get("woocommerce-session");u&&(p["woocommerce-session"]=u);let d=l.headers.get("set-cookie");return d&&(p["set-cookie"]=d),i.NextResponse.json(c,{status:l.status,headers:p})}catch(r){console.error("GraphQL proxy error:",r);let o=e.headers.get("origin")||"";return i.NextResponse.json({errors:[{message:r instanceof Error?r.message:"Unknown error occurred"}]},{status:500,headers:{"Access-Control-Allow-Origin":o,"Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, woocommerce-session, cookie","Access-Control-Allow-Credentials":"true",Vary:"Origin"}})}}async function c(e){let o=e.headers.get("origin")||"";return new i.NextResponse(null,{status:204,headers:{"Access-Control-Allow-Origin":o,"Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, woocommerce-session, cookie","Access-Control-Allow-Credentials":"true","Access-Control-Max-Age":"86400",Vary:"Origin"}})}let p=new t.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/graphql/route",pathname:"/api/graphql",filename:"route",bundlePath:"app/api/graphql/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\graphql\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:u,staticGenerationAsyncStorage:d,serverHooks:g}=p,h="/api/graphql/route";function A(){return(0,a.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:d})}}};var o=require("../../../webpack-runtime.js");o.C(e);var r=e=>o(o.s=e),s=o.X(0,[8948,5972],()=>r(18699));module.exports=s})();