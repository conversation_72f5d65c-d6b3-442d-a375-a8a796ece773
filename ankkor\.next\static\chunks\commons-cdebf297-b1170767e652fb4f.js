"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[986],{92371:function(t,r,e){e.r(r),e.d(r,{useCartStore:function(){return l},useWishlistStore:function(){return s}});var o=e(59625),i=e(89134),n=e(82372);let a={getItem:t=>{try{return localStorage.getItem(t)}catch(t){return console.error("localStorage.getItem error:",t),null}},setItem:(t,r)=>{try{localStorage.setItem(t,r)}catch(t){console.error("localStorage.setItem error:",t)}},removeItem:t=>{try{localStorage.removeItem(t)}catch(t){console.error("localStorage.removeItem error:",t)}}},c=(t,r)=>{try{if(!r||!r.lines){console.error("Invalid normalized cart data",r);return}let e=r.lines.reduce((t,r)=>t+(r.quantity||0),0),o=r.lines.map(t=>{var r;return{id:t.id,variantId:t.merchandise.id,productId:t.merchandise.product.id,title:t.merchandise.product.title,handle:t.merchandise.product.handle,image:(null===(r=t.merchandise.product.image)||void 0===r?void 0:r.url)||"",price:t.merchandise.price,quantity:t.quantity,currencyCode:t.merchandise.currencyCode}});t({items:o,subtotal:r.cost.subtotalAmount.amount,total:r.cost.totalAmount.amount,currencyCode:r.cost.totalAmount.currencyCode,itemCount:e,checkoutUrl:r.checkoutUrl,isLoading:!1})}catch(r){console.error("Error updating cart state:",r),t({items:[],subtotal:"0.00",total:"0.00",itemCount:0,isLoading:!1})}},l=(0,o.Ue)()((0,i.tJ)((t,r)=>({cartId:null,items:[],isOpen:!1,isLoading:!1,subtotal:"0.00",total:"0.00",currencyCode:"USD",itemCount:0,checkoutUrl:null,initializationInProgress:!1,initializationError:null,openCart:()=>t({isOpen:!0}),closeCart:()=>t({isOpen:!1}),toggleCart:()=>t(t=>({isOpen:!t.isOpen})),initCart:async()=>{let e=r();if(e.initializationInProgress)return console.log("Cart initialization already in progress, skipping"),null;t({isLoading:!0,initializationInProgress:!0,initializationError:null});try{if(e.cartId)try{let r=await (0,n.dv)();if(r)return t({isLoading:!1,initializationInProgress:!1}),r}catch(t){console.log("Existing cart validation failed, creating new cart")}let r=await (0,n.Bk)();if(r&&r.id)return t({cartId:r.id,checkoutUrl:r.checkoutUrl,isLoading:!1,initializationInProgress:!1}),console.log("Cart initialized with ID:",r.id),r;throw Error("Failed to create cart: No cart ID returned")}catch(r){return console.error("Failed to initialize cart:",r),t({isLoading:!1,initializationInProgress:!1,initializationError:r instanceof Error?r.message:"Unknown error initializing cart"}),null}},addItem:async e=>{t({isLoading:!0});try{if(!e.variantId)throw console.error("Cannot add item to cart: Missing variant ID",e),t({isLoading:!1}),Error("Missing variant ID for item");let o=r().cartId;if(!o){console.log("Cart not initialized, creating a new cart...");let t=await (0,n.Bk)();if(t&&t.id)console.log("New cart created:",t.id),o=t.id;else throw Error("Failed to initialize cart")}if(!o)throw Error("Failed to initialize cart: No cart ID available");console.log("Adding item to cart: ".concat(e.title," (").concat(e.variantId,"), quantity: ").concat(e.quantity));try{let r=await (0,n.Xq)(o,[{merchandiseId:e.variantId,quantity:e.quantity||1}]);if(!r)throw Error("Failed to add item to cart: No cart returned");let i=(0,n.Id)(r);c(t,i),t({isOpen:!0}),console.log("Item added to cart successfully. Cart now has ".concat(i.lines.length," items."))}catch(t){if(console.error("Shopify API error when adding to cart:",t),t instanceof Error)throw Error("Failed to add item to cart: ".concat(t.message));throw Error("Failed to add item to cart: Unknown API error")}}catch(r){throw console.error("Failed to add item to cart:",r),t({isLoading:!1}),r}},updateItem:async(e,o)=>{let i=r();t({isLoading:!0});try{if(!i.cartId)throw Error("Cart not initialized");if(console.log("Updating item in cart: ".concat(e,", new quantity: ").concat(o)),o<=0)return console.log("Quantity is ".concat(o,", removing item from cart")),r().removeItem(e);let a=await (0,n.xu)(i.cartId,[{id:e,quantity:o}]);if(!a)throw Error("Failed to update item: No cart returned");let l=(0,n.Id)(a);c(t,l),console.log("Item updated successfully. Cart now has ".concat(l.lines.length," items."))}catch(r){throw console.error("Failed to update item in cart:",r),t({isLoading:!1}),r}},removeItem:async e=>{let o=r();t({isLoading:!0});try{if(!o.cartId)throw console.error("Cannot remove item: Cart not initialized"),Error("Cart not initialized");console.log("Removing item from cart: ".concat(e));let r=[...o.items],i=r.find(t=>t.id===e);i?console.log('Removing "'.concat(i.title,'" (').concat(i.variantId,") from cart")):console.warn("Item with ID ".concat(e," not found in cart"));let a=await (0,n.h2)(o.cartId,[e]);if(!a)throw console.error("Failed to remove item: No cart returned from Shopify"),Error("Failed to remove item: No cart returned");let l=(0,n.Id)(a),s=l.lines.map(t=>({id:t.id,title:t.merchandise.product.title}));console.log("Cart before removal:",r.length,"items"),console.log("Cart after removal:",s.length,"items"),r.length===s.length&&console.warn("Item count did not change after removal operation"),c(t,l),console.log("Item removed successfully. Cart now has ".concat(l.lines.length," items."))}catch(r){throw console.error("Failed to remove item from cart:",r),t({isLoading:!1}),r}},clearCart:async()=>{r(),t({isLoading:!0});try{console.log("Clearing cart and creating a new one");let r=await (0,n.Bk)();if(!r)throw Error("Failed to create new cart");t({cartId:r.id,items:[],subtotal:"0.00",total:"0.00",itemCount:0,checkoutUrl:r.checkoutUrl,isLoading:!1}),console.log("Cart cleared successfully. New cart ID:",r.id)}catch(r){throw console.error("Failed to clear cart:",r),t({isLoading:!1}),r}}}),{name:"ankkor-cart",storage:(0,i.FL)(()=>a),version:1,partialize:t=>({cartId:t.cartId,items:t.items,subtotal:t.subtotal,total:t.total,currencyCode:t.currencyCode,itemCount:t.itemCount,checkoutUrl:t.checkoutUrl})})),s=(0,o.Ue)()((0,i.tJ)((t,r)=>({items:[],isLoading:!1,addToWishlist:r=>{t(t=>t.items.some(t=>t.id===r.id)?t:{items:[...t.items,r]})},removeFromWishlist:r=>{t(t=>({items:t.items.filter(t=>t.id!==r)}))},clearWishlist:()=>{t({items:[]})},isInWishlist:t=>r().items.some(r=>r.id===t)}),{name:"ankkor-wishlist",storage:(0,i.FL)(()=>a),partialize:t=>({items:t.items}),skipHydration:!0}))},93448:function(t,r,e){e.d(r,{cn:function(){return n}});var o=e(61994),i=e(53335);function n(){for(var t=arguments.length,r=Array(t),e=0;e<t;e++)r[e]=arguments[e];return(0,i.m6)((0,o.W)(r))}},87466:function(t,r,e){e.d(r,{xh:function(){return u}});var o=e(29865),i=e(40257);let n=new o.s({url:i.env.UPSTASH_REDIS_REST_URL||i.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:i.env.UPSTASH_REDIS_REST_TOKEN||i.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""}),a={},c={};function l(){return!!(i.env.UPSTASH_REDIS_REST_URL&&i.env.UPSTASH_REDIS_REST_TOKEN||i.env.NEXT_PUBLIC_KV_REST_API_URL&&i.env.NEXT_PUBLIC_KV_REST_API_TOKEN)}async function s(t){try{if(l())return await n.get("".concat("woo:inventory:mapping:").concat(t))||null;return a[t]||null}catch(r){console.error("Error getting product slug from Redis:",r);try{return a[t]||null}catch(t){return console.error("Error getting from memory fallback:",t),null}}}async function d(t){try{if(l())return await n.hget("shopify:to:woo:mapping",t)||null;return c[t]||null}catch(r){console.error("Error getting WooCommerce ID for Shopify ID ".concat(t,":"),r);try{return c[t]||null}catch(t){return console.error("Error getting from memory fallback:",t),null}}}async function u(t){if(!t||"undefined"===t||"null"===t)return console.warn("Invalid product ID received:",t),t;if(t.includes("gid://shopify/Product/")){console.log("Detected Shopify ID: ".concat(t,", attempting to map to WooCommerce ID"));let r=await d(t);return r?(console.log("Mapped Shopify ID ".concat(t," to WooCommerce ID ").concat(r)),r):(console.warn("No mapping found for Shopify ID: ".concat(t,", using original ID")),t)}return t.includes("=")?await s(t)||console.warn("Product ID ".concat(t," not found in inventory mapping, using as is")):/^\d+$/.test(t),t}}}]);