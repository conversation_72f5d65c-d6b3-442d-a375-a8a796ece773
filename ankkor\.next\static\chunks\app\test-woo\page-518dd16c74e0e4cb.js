(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2483],{55779:function(e,s,t){Promise.resolve().then(t.bind(t,55032))},55032:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return n}});var l=t(57437),r=t(2265);function n(){var e,s;let[n,a]=(0,r.useState)(null),[o,i]=(0,r.useState)(!0),[c,d]=(0,r.useState)(null);return((0,r.useEffect)(()=>{(async()=>{try{i(!0),d(null),console.log("\uD83E\uDDEA Starting WooCommerce tests...");let{testWooCommerceConnection:n,getAllCategories:o,getCategoryProducts:c}=await Promise.all([t.e(5540),t.e(4822),t.e(8787),t.e(8261),t.e(1539),t.e(2188),t.e(6003),t.e(7231),t.e(2323),t.e(9960),t.e(7696),t.e(8002),t.e(7111),t.e(8989),t.e(4513),t.e(8476),t.e(8496),t.e(8966),t.e(3903),t.e(6076),t.e(4596),t.e(62),t.e(6271),t.e(8726),t.e(8133),t.e(8049),t.e(2870),t.e(632),t.e(6613),t.e(9965),t.e(5270),t.e(2647),t.e(9971),t.e(9290),t.e(6459),t.e(7800),t.e(4818),t.e(1729),t.e(5526),t.e(4615),t.e(7406),t.e(2532),t.e(2068),t.e(4595),t.e(3280),t.e(8790),t.e(1104),t.e(7158),t.e(7044),t.e(9482),t.e(6628),t.e(986),t.e(4754)]).then(t.bind(t,82372));console.log("\uD83D\uDD17 Testing basic connection...");let u=await n();console.log("Connection test result:",u),console.log("\uD83D\uDCC2 Testing categories...");let m=await o(20);console.log("Categories result:",m),console.log("\uD83D\uDC54 Testing shirts category...");let x=await c("shirts",{first:10});console.log("Shirts category result:",x);let g=[];for(let t of["shirt","Shirts","clothing","apparel"]){console.log("\uD83D\uDD0D Testing category: ".concat(t));try{var e,s,l,r;let n=await c(t,{first:5});g.push({name:t,success:!!(null==n?void 0:null===(s=n.products)||void 0===s?void 0:null===(e=s.nodes)||void 0===e?void 0:e.length),productCount:(null==n?void 0:null===(r=n.products)||void 0===r?void 0:null===(l=r.nodes)||void 0===l?void 0:l.length)||0,result:n})}catch(e){g.push({name:t,success:!1,error:e instanceof Error?e.message:"Unknown error"})}}a({connectionTest:u,categories:null==m?void 0:m.slice(0,10),categoriesCount:(null==m?void 0:m.length)||0,shirtsCategory:x,alternativeTests:g,timestamp:new Date().toISOString()})}catch(e){console.error("❌ Test failed:",e),d(e instanceof Error?e.message:"Unknown error")}finally{i(!1)}})()},[]),o)?(0,l.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Testing WooCommerce connection..."})]})}):c?(0,l.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,l.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-red-600 mb-6",children:"WooCommerce Test Failed"}),(0,l.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:(0,l.jsx)("p",{className:"text-red-800",children:c})})]})}):(0,l.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,l.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"WooCommerce Connection Test Results"}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"\uD83D\uDD17 Connection Test"}),(0,l.jsx)("div",{className:"bg-gray-50 rounded p-4",children:(0,l.jsx)("pre",{className:"text-sm overflow-auto",children:JSON.stringify(null==n?void 0:n.connectionTest,null,2)})})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,l.jsxs)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:["\uD83D\uDCC2 Categories (",(null==n?void 0:n.categoriesCount)||0," total)"]}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:null==n?void 0:null===(e=n.categories)||void 0===e?void 0:e.map((e,s)=>(0,l.jsxs)("div",{className:"bg-gray-50 rounded p-3",children:[(0,l.jsx)("h3",{className:"font-medium text-gray-900",children:e.name}),(0,l.jsxs)("p",{className:"text-sm text-gray-600",children:["Slug: ",e.slug]}),(0,l.jsxs)("p",{className:"text-sm text-gray-600",children:["Count: ",e.count||0]}),(0,l.jsxs)("p",{className:"text-sm text-gray-600",children:["ID: ",e.id]})]},s))})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"\uD83D\uDC54 Shirts Category Test"}),(0,l.jsx)("div",{className:"bg-gray-50 rounded p-4",children:(0,l.jsx)("pre",{className:"text-sm overflow-auto",children:JSON.stringify(null==n?void 0:n.shirtsCategory,null,2)})})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"\uD83D\uDD0D Alternative Category Tests"}),(0,l.jsx)("div",{className:"space-y-4",children:null==n?void 0:null===(s=n.alternativeTests)||void 0===s?void 0:s.map((e,s)=>(0,l.jsxs)("div",{className:"border rounded p-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,l.jsxs)("h3",{className:"font-medium text-gray-900",children:['Category: "',e.name,'"']}),(0,l.jsx)("span",{className:"px-2 py-1 rounded text-sm ".concat(e.success?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.success?"✅ ".concat(e.productCount," products"):"❌ Failed"})]}),e.error&&(0,l.jsx)("p",{className:"text-red-600 text-sm",children:e.error}),e.result&&(0,l.jsxs)("details",{className:"mt-2",children:[(0,l.jsx)("summary",{className:"cursor-pointer text-sm text-gray-600",children:"View raw result"}),(0,l.jsx)("pre",{className:"text-xs bg-gray-50 p-2 mt-2 rounded overflow-auto",children:JSON.stringify(e.result,null,2)})]})]},s))})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"\uD83D\uDCCA Raw Test Results"}),(0,l.jsx)("div",{className:"bg-gray-50 rounded p-4",children:(0,l.jsx)("pre",{className:"text-xs overflow-auto max-h-96",children:JSON.stringify(n,null,2)})})]})]})})}}},function(e){e.O(0,[5540,4822,8787,8261,1539,2188,6003,7231,2323,9960,7696,8002,7111,8989,4513,8476,8496,8966,3903,6076,4596,62,6271,8726,8133,8049,2870,1744],function(){return e(e.s=55779)}),_N_E=e.O()}]);