"use strict";(()=>{var e={};e.id=7520,e.ids=[7520],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},58078:(e,o,r)=>{r.r(o),r.d(o,{originalPathname:()=>l,patchFetch:()=>f,requestAsyncStorage:()=>d,routeModule:()=>u,serverHooks:()=>m,staticGenerationAsyncStorage:()=>h});var t={};r.r(t),r.d(t,{GET:()=>p,dynamic:()=>i});var n=r(49303),a=r(88716),s=r(60670),c=r(87070);let i="force-dynamic";async function p(e){try{let o="https://maroon-lapwing-781450.hostingersite.com";if(!o)throw Error("WooCommerce URL not configured");let r=e.headers.get("Cart-Token"),t=await fetch(`${o}/wp-json/wc/store/v1/cart`,{method:"GET",headers:{"Content-Type":"application/json",...r?{"Cart-Token":r}:{}},credentials:"include",cache:"no-store"});if(!t.ok)throw Error(`Failed to fetch nonce: ${t.status}`);let n=t.headers.get("X-WC-Store-API-Nonce");if(!n){let e=await t.json();e.extensions&&e.extensions.store_api_nonce&&(n=e.extensions.store_api_nonce)}if(!n)throw Error("No nonce returned from WooCommerce");return c.NextResponse.json({nonce:n},{headers:{"Cache-Control":"no-store, max-age=0"}})}catch(e){return console.error("Error fetching WooCommerce nonce:",e),c.NextResponse.json({success:!1,message:e instanceof Error?e.message:"An error occurred fetching the nonce"},{status:500})}}let u=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/nonce/route",pathname:"/api/nonce",filename:"route",bundlePath:"app/api/nonce/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\nonce\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:d,staticGenerationAsyncStorage:h,serverHooks:m}=u,l="/api/nonce/route";function f(){return(0,s.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:h})}}};var o=require("../../../webpack-runtime.js");o.C(e);var r=e=>o(o.s=e),t=o.X(0,[8948,5972],()=>r(58078));module.exports=t})();