"use strict";(()=>{var e={};e.id=3517,e.ids=[3517],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},85101:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>m,patchFetch:()=>y,requestAsyncStorage:()=>p,routeModule:()=>u,serverHooks:()=>l,staticGenerationAsyncStorage:()=>d});var o={};r.r(o),r.d(o,{GET:()=>c});var s=r(49303),a=r(88716),n=r(60670),i=r(87070);async function c(){try{let e="rzp_live_H1Iyl4j48eSFYj",t=process.env.RAZORPAY_KEY_SECRET;if(!e||!t)return i.NextResponse.json({success:!1,error:"Razorpay credentials not configured",details:{keyId:e?"Configured":"Missing",keySecret:t?"Configured":"Missing"}},{status:500});let r=Buffer.from(`${e}:${t}`).toString("base64"),o=await fetch("https://api.razorpay.com/v1/account",{method:"GET",headers:{Authorization:`Basic ${r}`,"Content-Type":"application/json"}});if(!o.ok){let e=await o.json();return i.NextResponse.json({success:!1,error:"Razorpay API authentication failed",details:e,status:o.status},{status:o.status})}let s=await o.json();return i.NextResponse.json({success:!0,message:"Razorpay connection successful",account:{id:s.id,name:s.name,email:s.email,status:s.status,live_mode:s.live_mode}})}catch(e){return console.error("Razorpay connection test error:",e),i.NextResponse.json({success:!1,error:"Failed to test Razorpay connection",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let u=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/razorpay/test-connection/route",pathname:"/api/razorpay/test-connection",filename:"route",bundlePath:"app/api/razorpay/test-connection/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\razorpay\\test-connection\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:p,staticGenerationAsyncStorage:d,serverHooks:l}=u,m="/api/razorpay/test-connection/route";function y(){return(0,n.patchFetch)({serverHooks:l,staticGenerationAsyncStorage:d})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,5972],()=>r(85101));module.exports=o})();