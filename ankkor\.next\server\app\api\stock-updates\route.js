"use strict";(()=>{var e={};e.id=7248,e.ids=[7248],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},42654:(e,t,a)=>{a.r(t),a.d(t,{originalPathname:()=>m,patchFetch:()=>v,requestAsyncStorage:()=>S,routeModule:()=>d,serverHooks:()=>k,staticGenerationAsyncStorage:()=>g});var s={};a.r(s),a.d(s,{GET:()=>u,POST:()=>l});var r=a(49303),o=a(88716),n=a(60670),i=a(87070),c=a(94868);let p=process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN?new c.s({url:process.env.UPSTASH_REDIS_REST_URL,token:process.env.UPSTASH_REDIS_REST_TOKEN}):null;async function u(e){let{searchParams:t}=new URL(e.url),a=t.get("products")?.split(",")||[],s=new Headers({"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","Access-Control-Allow-Headers":"Cache-Control"});return new Response(new ReadableStream({start(t){t.enqueue(`data: ${JSON.stringify({type:"connected",message:"Stock updates stream connected",timestamp:new Date().toISOString()})}

`);let s=async()=>{if(!p){console.log("Redis not available, skipping stock update check");return}try{for(let e of a){let a=await p.get(`stock_update:${e}`);a&&(t.enqueue(`data: ${JSON.stringify({type:"stock_update",productId:e,...a,timestamp:new Date().toISOString()})}

`),await p.del(`stock_update:${e}`))}}catch(e){console.error("Error checking stock updates:",e),t.enqueue(`data: ${JSON.stringify({type:"error",message:"Stock update service temporarily unavailable",timestamp:new Date().toISOString()})}

`)}},r=null;p?(r=setInterval(s,5e3),console.log("Started stock updates polling (Redis available)")):(console.log("Redis not available, stock updates polling disabled"),t.enqueue(`data: ${JSON.stringify({type:"service_unavailable",message:"Real-time stock updates are currently unavailable",timestamp:new Date().toISOString()})}

`));let o=setInterval(()=>{t.enqueue(`data: ${JSON.stringify({type:"heartbeat",timestamp:new Date().toISOString()})}

`)},3e4),n=()=>{r&&clearInterval(r),clearInterval(o),t.close()};e.signal.addEventListener("abort",n),setTimeout(n,3e5)}}),{headers:s})}async function l(e){try{let{productId:t,stockData:a}=await e.json();if(!t||!a)return i.NextResponse.json({error:"productId and stockData are required"},{status:400});return p&&await p.set(`stock_update:${t}`,a,60),i.NextResponse.json({success:!0,message:"Stock update notification sent",productId:t,timestamp:new Date().toISOString()})}catch(e){return console.error("Error sending stock update notification:",e),i.NextResponse.json({error:"Failed to send stock update notification"},{status:500})}}let d=new r.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/stock-updates/route",pathname:"/api/stock-updates",filename:"route",bundlePath:"app/api/stock-updates/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\stock-updates\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:S,staticGenerationAsyncStorage:g,serverHooks:k}=d,m="/api/stock-updates/route";function v(){return(0,n.patchFetch)({serverHooks:k,staticGenerationAsyncStorage:g})}}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[8948,5972,4766,4868],()=>a(42654));module.exports=s})();