"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6628],{94348:function(t,e,o){o.r(e),o.d(e,{addInventoryMapping:function(){return d},clearInventoryMappings:function(){return p},getAllInventoryMappings:function(){return y},getProductHandleFromInventory:function(){return g},loadInventoryMap:function(){return l},saveInventoryMap:function(){return u},updateInventoryMappings:function(){return m}});var r=o(29865),n=o(40257);let a="inventory:mapping:",c=new r.s({url:n.env.UPSTASH_REDIS_REST_URL||n.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:n.env.UPSTASH_REDIS_REST_TOKEN||n.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""}),i={};function s(){return!!(n.env.UPSTASH_REDIS_REST_URL&&n.env.UPSTASH_REDIS_REST_TOKEN||n.env.NEXT_PUBLIC_KV_REST_API_URL&&n.env.NEXT_PUBLIC_KV_REST_API_TOKEN)}async function l(){if(!s())return{...i};try{let t=await c.keys("".concat(a,"*"));if(0===t.length)return console.log("No existing inventory mappings found in Redis"),{};let e={},o=await c.mget(...t);return t.forEach((t,r)=>{let n=t.replace(a,""),c=o[r];e[n]=c}),console.log("Loaded inventory mapping with ".concat(Object.keys(e).length," entries from Redis")),e}catch(t){return console.error("Error loading inventory mapping from Redis:",t),console.log("Falling back to in-memory storage"),{...i}}}async function u(t){if(s())try{let e=c.pipeline(),o=await c.keys("".concat(a,"*"));o.length>0&&e.del(...o),Object.entries(t).forEach(t=>{let[o,r]=t;e.set("".concat(a).concat(o),r)}),await e.exec(),console.log("Saved inventory mapping with ".concat(Object.keys(t).length," entries to Redis"))}catch(e){console.error("Error saving inventory mapping to Redis:",e),console.log("Falling back to in-memory storage"),Object.assign(i,t)}else Object.assign(i,t),console.log("Saved inventory mapping with ".concat(Object.keys(t).length," entries to memory"))}async function d(t,e){try{return s()?(await c.set("".concat(a).concat(t),e),console.log("Added mapping to Redis: ".concat(t," -> ").concat(e))):(i[t]=e,console.log("Added mapping to memory: ".concat(t," -> ").concat(e))),!0}catch(o){console.error("Error adding inventory mapping:",o);try{return i[t]=e,console.log("Added mapping to memory fallback: ".concat(t," -> ").concat(e)),!0}catch(t){return console.error("Error adding to memory fallback:",t),!1}}}async function g(t){try{if(s())return await c.get("".concat(a).concat(t))||null;return i[t]||null}catch(e){console.error("Error getting product handle from Redis:",e);try{return i[t]||null}catch(t){return console.error("Error getting from memory fallback:",t),null}}}async function m(t){try{if(s()){let e=c.pipeline();for(let{inventoryItemId:o,productHandle:r}of t)e.set("".concat(a).concat(o),r);await e.exec(),console.log("Updated ".concat(t.length," inventory mappings in Redis"))}else{for(let{inventoryItemId:e,productHandle:o}of t)i[e]=o;console.log("Updated ".concat(t.length," inventory mappings in memory"))}return!0}catch(e){console.error("Error updating inventory mappings in Redis:",e);try{for(let{inventoryItemId:e,productHandle:o}of t)i[e]=o;return console.log("Updated ".concat(t.length," inventory mappings in memory fallback")),!0}catch(t){return console.error("Error updating in memory fallback:",t),!1}}}async function y(){return await l()}async function p(){try{if(s()){let t=await c.keys("".concat(a,"*"));t.length>0&&await c.del(...t),console.log("Cleared all inventory mappings from Redis")}else Object.keys(i).forEach(t=>{delete i[t]}),console.log("Cleared all inventory mappings from memory");return!0}catch(t){return console.error("Error clearing inventory mappings:",t),!1}}},87758:function(t,e,o){o.r(e),o.d(e,{clearCartAfterCheckout:function(){return p},formatPrice:function(){return y},useLocalCartCount:function(){return l},useLocalCartError:function(){return m},useLocalCartItems:function(){return s},useLocalCartLoading:function(){return g},useLocalCartStore:function(){return i},useLocalCartSubtotal:function(){return u},useLocalCartTotal:function(){return d}});var r=o(59625),n=o(89134);let a=()=>Math.random().toString(36).substring(2,15),c=async(t,e,o)=>{try{let r=await fetch("/api/products/".concat(t,"/stock").concat(o?"?variation_id=".concat(o):""));if(!r.ok)return console.warn("Stock validation API failed, allowing add to cart"),{available:!0,message:"Stock validation temporarily unavailable"};let n=await r.json();if("IN_STOCK"!==n.stockStatus&&"instock"!==n.stockStatus)return{available:!1,message:"This product is currently out of stock",stockStatus:n.stockStatus};if(null!==n.stockQuantity&&n.stockQuantity<e)return{available:!1,message:"Only ".concat(n.stockQuantity," items available in stock"),stockQuantity:n.stockQuantity,stockStatus:n.stockStatus};return{available:!0,stockQuantity:n.stockQuantity,stockStatus:n.stockStatus}}catch(t){return console.error("Stock validation error:",t),console.warn("Stock validation failed, allowing add to cart for better UX"),{available:!0,message:"Stock validation temporarily unavailable"}}},i=(0,r.Ue)()((0,n.tJ)((t,e)=>({items:[],itemCount:0,isLoading:!1,error:null,addToCart:async o=>{t({isLoading:!0,error:null});try{let r,n;let i=await c(o.productId,o.quantity,o.variationId);if(!i.available)throw Error(i.message||"Product is out of stock");try{let t=await fetch("/api/reservations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"create",productId:o.productId,quantity:o.quantity,userId:"cart_user_".concat(Date.now()),variationId:o.variationId})}),e=await t.json();e.success&&e.reservation?(r=e.reservation.id,n=e.reservation.expiresAt,console.log("Stock reserved for ".concat(o.name,": ").concat(r," (expires: ").concat(n,")"))):console.warn("Failed to create stock reservation:",e.error)}catch(t){console.warn("Stock reservation failed, continuing without reservation:",t)}let s=e().items,l=o.price;"string"==typeof l&&(l=l.replace(/[₹$€£]/g,"").trim().replace(/,/g,""));let u={...o,price:l},d=s.findIndex(t=>t.productId===u.productId&&t.variationId===u.variationId);if(-1!==d){let e=[...s];e[d].quantity+=u.quantity,r&&(e[d].reservationId=r,e[d].reservedUntil=n),t({items:e,itemCount:e.reduce((t,e)=>t+e.quantity,0),isLoading:!1})}else{let e={...u,id:a(),reservationId:r,reservedUntil:n};t({items:[...s,e],itemCount:s.reduce((t,e)=>t+e.quantity,0)+e.quantity,isLoading:!1})}console.log("Item added to cart successfully");try{let t={state:{items:e().items,itemCount:e().itemCount,isLoading:!1,error:null},version:1};localStorage.setItem("ankkor-local-cart",JSON.stringify(t))}catch(t){console.warn("Failed to manually persist cart to localStorage:",t)}}catch(e){console.error("Error adding item to cart:",e),t({error:e instanceof Error?e.message:"An unknown error occurred",isLoading:!1})}},updateCartItem:(o,r)=>{t({isLoading:!0,error:null});try{let n=e().items;if(r<=0)return e().removeCartItem(o);let a=n.map(t=>t.id===o?{...t,quantity:r}:t);t({items:a,itemCount:a.reduce((t,e)=>t+e.quantity,0),isLoading:!1});try{let t={state:{items:a,itemCount:a.reduce((t,e)=>t+e.quantity,0),isLoading:!1,error:null},version:1};localStorage.setItem("ankkor-local-cart",JSON.stringify(t))}catch(t){console.warn("Failed to manually persist cart update to localStorage:",t)}}catch(e){console.error("Error updating cart item:",e),t({error:e instanceof Error?e.message:"An unknown error occurred",isLoading:!1})}},removeCartItem:o=>{t({isLoading:!0,error:null});try{let r=e().items.filter(t=>t.id!==o);t({items:r,itemCount:r.reduce((t,e)=>t+e.quantity,0),isLoading:!1});try{let t={state:{items:r,itemCount:r.reduce((t,e)=>t+e.quantity,0),isLoading:!1,error:null},version:1};localStorage.setItem("ankkor-local-cart",JSON.stringify(t))}catch(t){console.warn("Failed to manually persist cart removal to localStorage:",t)}}catch(e){console.error("Error removing cart item:",e),t({error:e instanceof Error?e.message:"An unknown error occurred",isLoading:!1})}},clearCart:()=>{t({items:[],itemCount:0,isLoading:!1,error:null});try{localStorage.setItem("ankkor-local-cart",JSON.stringify({state:{items:[],itemCount:0,isLoading:!1,error:null},version:1}))}catch(t){console.warn("Failed to manually persist cart clearing to localStorage:",t)}},setError:e=>{t({error:e})},setIsLoading:e=>{t({isLoading:e})},subtotal:()=>{let t=e().items;try{let e=t.reduce((t,e)=>{let o=0;if("string"==typeof e.price){let t=e.price.replace(/[₹$€£]/g,"").trim().replace(/,/g,"");o=parseFloat(t)}else o=e.price;return isNaN(o)?(console.warn("Invalid price for item ".concat(e.id,": ").concat(e.price)),t):t+o*e.quantity},0);return isNaN(e)?0:e}catch(t){return console.error("Error calculating subtotal:",t),0}},total:()=>{let t=e().subtotal();return isNaN(t)?0:t},syncWithWooCommerce:async o=>{let{items:r}=e();if(0===r.length)throw Error("Cart is empty");try{if(console.log("Syncing cart with WooCommerce..."),console.log("Auth token provided:",!!o),t({isLoading:!0}),o){console.log("User is authenticated, using JWT-to-Cookie bridge");try{let e=await f(o,r);return t({isLoading:!1}),e}catch(t){console.error("JWT-to-Cookie bridge failed:",t),console.log("Falling back to guest checkout...")}}console.log("User is not authenticated, redirecting to WooCommerce checkout");let e="".concat("https://maroon-lapwing-781450.hostingersite.com","/checkout/");return console.log("Guest checkout URL:",e),t({isLoading:!1}),e}catch(e){console.error("Error syncing cart with WooCommerce:",e),t({isLoading:!1});try{console.log("Attempting fallback method for cart sync...");let t="".concat("https://maroon-lapwing-781450.hostingersite.com","/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1");return r.forEach((e,o)=>{0===o?t+="&add-to-cart=".concat(e.productId,"&quantity=").concat(e.quantity):t+="&add-to-cart[".concat(o,"]=").concat(e.productId,"&quantity[").concat(o,"]=").concat(e.quantity),e.variationId&&(t+="&variation_id=".concat(e.variationId))}),console.log("Fallback checkout URL:",t),t}catch(t){throw console.error("Fallback method failed:",t),Error("Failed to sync cart with WooCommerce. Please try again or contact support.")}}}}),{name:"ankkor-local-cart",version:1,skipHydration:!0})),s=()=>i(t=>t.items),l=()=>i(t=>t.itemCount),u=()=>i(t=>t.subtotal()),d=()=>i(t=>t.total()),g=()=>i(t=>t.isLoading),m=()=>i(t=>t.error),y=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"INR",o="string"==typeof t?parseFloat(t):t;return new Intl.NumberFormat("en-IN",{style:"currency",currency:e,minimumFractionDigits:2,maximumFractionDigits:2}).format(o)},p=()=>{i.getState().clearCart()};async function f(t,e){if(!t)throw Error("Authentication token is required");let o="https://maroon-lapwing-781450.hostingersite.com",r="https://maroon-lapwing-781450.hostingersite.com/checkout/";if(!o||!r)throw Error("WordPress or checkout URL not configured. Check your environment variables.");try{console.log("Creating WordPress session from JWT token..."),console.log("Using endpoint:","".concat(o,"/wp-json/headless/v1/create-wp-session")),console.log("Token length:",t.length),console.log("Token preview:",t.substring(0,20)+"...");let e=await fetch("".concat(o,"/wp-json/headless/v1/create-wp-session"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify({token:t}),credentials:"include"});if(console.log("Response status:",e.status),console.log("Response headers:",Object.fromEntries(e.headers.entries())),!e.ok){let t="HTTP ".concat(e.status,": ").concat(e.statusText);try{let o=await e.json();t=o.message||o.code||t,console.error("Error response data:",o)}catch(t){console.error("Could not parse error response:",t)}throw Error("Failed to create WordPress session: ".concat(t))}let n=await e.json();if(console.log("Response data:",n),!n.success)throw Error(n.message||"Failed to create WordPress session");return console.log("WordPress session created successfully"),console.log("Redirecting to checkout URL:",r),r}catch(t){if(console.error("Error creating WordPress session:",t),t instanceof TypeError&&t.message.includes("fetch"))throw Error("Network error: Could not connect to WordPress. Please check your internet connection.");throw Error(t instanceof Error?t.message:"Failed to prepare checkout")}}},82429:function(t,e,o){function r(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"INR";switch(t){case"INR":return"₹";case"USD":return"$";case"EUR":return"€";case"GBP":return"\xa3";default:return t}}o.d(e,{jK:function(){return r}}),o(94348)}}]);