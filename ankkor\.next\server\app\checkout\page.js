(()=>{var e={};e.id=285,e.ids=[285],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},824:(e,a,r)=>{"use strict";r.r(a),r.d(a,{GlobalError:()=>t.Z,__next_app__:()=>h,originalPathname:()=>c,pages:()=>d,routeModule:()=>m,tree:()=>l}),r(51324),r(11360),r(7629),r(11930),r(12523);var i=r(23191),s=r(88716),t=r(43315),n=r(95231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(a,o);let l=["",{children:["checkout",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,51324)),"E:\\ankkorwoo\\ankkor\\src\\app\\checkout\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,11360)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,7629)),"E:\\ankkorwoo\\ankkor\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,11930)),"E:\\ankkorwoo\\ankkor\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],d=["E:\\ankkorwoo\\ankkor\\src\\app\\checkout\\page.tsx"],c="/checkout/page",h={require:r,loadChunk:()=>Promise.resolve()},m=new i.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/checkout/page",pathname:"/checkout",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},91860:(e,a,r)=>{Promise.resolve().then(r.bind(r,50452))},28916:(e,a,r)=>{"use strict";r.d(a,{Z:()=>i});let i=(0,r(76557).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},14228:(e,a,r)=>{"use strict";r.d(a,{Z:()=>i});let i=(0,r(76557).Z)("Truck",[["path",{d:"M5 18H3c-.6 0-1-.4-1-1V7c0-.6.4-1 1-1h10c.6 0 1 .4 1 1v11",key:"hs4xqm"}],["path",{d:"M14 9h4l4 4v4c0 .6-.4 1-1 1h-2",key:"11fp61"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}]])},50452:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>C});var i=r(10326),s=r(17577),t=r(35047),n=r(74723),o=r(86806),l=r(60114),d=r(85251);let c=async(e,a,r={})=>{try{if(!e||e<=0)throw Error("Invalid amount");if(e<1)throw Error("Minimum order amount is ₹1");console.log("Creating Razorpay order:",{amount:e,receipt:a,notes:r});let i=await fetch("/api/razorpay/create-order",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({amount:Math.round(100*e),receipt:a,notes:r})});if(!i.ok){let e=await i.json();if(console.error("Razorpay order creation failed:",e),400===i.status)throw Error(e.error||"Invalid order data");if(500===i.status)throw Error("Payment gateway error. Please try again.");throw Error(e.error||"Failed to create payment order")}let s=await i.json();return console.log("Razorpay order created successfully:",s.id),s}catch(e){if(console.error("Error creating Razorpay order:",e),e instanceof Error)throw e;throw Error("Failed to create payment order")}},h=async(e,a)=>{try{let r=await fetch("/api/razorpay/verify-payment",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({razorpay_payment_id:e.razorpay_payment_id,razorpay_order_id:e.razorpay_order_id,razorpay_signature:e.razorpay_signature,address:a.address,cartItems:a.cartItems,shipping:a.shipping})});if(!r.ok){let e=await r.json();throw Error(e.message||"Payment verification failed")}return await r.json()}catch(e){throw console.error("Error verifying payment:",e),e}},m=e=>new Promise((e,a)=>{try{a(Error("Razorpay SDK not loaded"));return}catch(e){console.error("Error initializing Razorpay:",e),a(e)}}),p=async(e,a,r)=>{try{let i=await fetch("/api/shipping-rates",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({pincode:e,cartItems:a,state:r})});if(!i.ok){let e=await i.json();throw Error(e.error||"Failed to get shipping rates")}return await i.json()}catch(e){throw console.error("Error getting shipping rates:",e),e}},u=(0,l.Ue)()((0,d.tJ)((e,a)=>({cart:[],shippingAddress:null,shippingOptions:[],selectedShipping:null,subtotal:0,shippingCost:0,finalAmount:0,isLoadingShipping:!1,isProcessingPayment:!1,error:null,setCart:r=>{let i=r.reduce((e,a)=>e+("string"==typeof a.price?parseFloat(a.price):a.price)*a.quantity,0),{shippingCost:s}=a();e({cart:r,subtotal:i,finalAmount:i+s})},setShippingAddress:a=>{e({shippingAddress:a})},fetchShippingRates:async(r,i)=>{let{cart:s,subtotal:t}=a();if(!i){e({error:"Please select a state"});return}e({isLoadingShipping:!0,error:null});try{let a=await p(r||"000000",s,i),n=a.length>0?a[0]:null,o=n?n.cost:0;e({shippingOptions:a,isLoadingShipping:!1,selectedShipping:n,shippingCost:o,finalAmount:t+o})}catch(a){console.error("Error fetching shipping rates:",a),e({error:a instanceof Error?a.message:"Failed to fetch shipping rates",isLoadingShipping:!1,shippingOptions:[]})}},setSelectedShipping:r=>{let{subtotal:i}=a(),s=i+r.cost;e({selectedShipping:r,shippingCost:r.cost,finalAmount:s})},calculateFinalAmount:()=>{let{subtotal:r,shippingCost:i,finalAmount:s}=a(),t=r+i;t!==s&&e({finalAmount:t})},setError:a=>{e({error:a})},setProcessingPayment:a=>{e({isProcessingPayment:a})},clearCheckout:()=>{e({cart:[],shippingAddress:null,shippingOptions:[],selectedShipping:null,subtotal:0,shippingCost:0,finalAmount:0,isLoadingShipping:!1,isProcessingPayment:!1,error:null})}}),{name:"checkout-storage",partialize:e=>({shippingAddress:e.shippingAddress,selectedShipping:e.selectedShipping})}));var g=r(68897),f=r(72248),x=r(41190),y=r(45226),b=s.forwardRef((e,a)=>(0,i.jsx)(y.WV.label,{...e,ref:a,onMouseDown:a=>{a.target.closest("button, input, select, textarea")||(e.onMouseDown?.(a),!a.defaultPrevented&&a.detail>1&&a.preventDefault())}}));b.displayName="Label";var v=r(51223);function j({className:e,...a}){return i.jsx(b,{"data-slot":"label",className:(0,v.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...a})}var N=r(75290),w=r(14228),k=r(28916);let P=[{name:"Punjab",code:"PB",cities:["Amritsar","Ludhiana","Jalandhar","Patiala","Bathinda","Mohali","Pathankot","Moga","Abohar","Malerkotla"]},{name:"Delhi",code:"DL",cities:["New Delhi","Delhi","North Delhi","South Delhi","East Delhi","West Delhi","Central Delhi"]},{name:"Maharashtra",code:"MH",cities:["Mumbai","Pune","Nagpur","Nashik","Aurangabad","Solapur","Amravati","Kolhapur","Sangli","Malegaon"]},{name:"Karnataka",code:"KA",cities:["Bangalore","Mysore","Hubli","Mangalore","Belgaum","Gulbarga","Davanagere","Bellary","Bijapur","Shimoga"]},{name:"Tamil Nadu",code:"TN",cities:["Chennai","Coimbatore","Madurai","Tiruchirappalli","Salem","Tirunelveli","Tiruppur","Vellore","Erode","Thoothukudi"]},{name:"Gujarat",code:"GJ",cities:["Ahmedabad","Surat","Vadodara","Rajkot","Bhavnagar","Jamnagar","Junagadh","Gandhinagar","Anand","Navsari"]},{name:"Rajasthan",code:"RJ",cities:["Jaipur","Jodhpur","Udaipur","Kota","Bikaner","Ajmer","Bhilwara","Alwar","Bharatpur","Sikar"]},{name:"West Bengal",code:"WB",cities:["Kolkata","Howrah","Durgapur","Asansol","Siliguri","Malda","Bardhaman","Baharampur","Habra","Kharagpur"]},{name:"Uttar Pradesh",code:"UP",cities:["Lucknow","Kanpur","Ghaziabad","Agra","Varanasi","Meerut","Allahabad","Bareilly","Aligarh","Moradabad"]},{name:"Haryana",code:"HR",cities:["Gurgaon","Faridabad","Panipat","Ambala","Yamunanagar","Rohtak","Hisar","Karnal","Sonipat","Panchkula"]},{name:"Madhya Pradesh",code:"MP",cities:["Bhopal","Indore","Gwalior","Jabalpur","Ujjain","Sagar","Dewas","Satna","Ratlam","Rewa"]},{name:"Bihar",code:"BR",cities:["Patna","Gaya","Bhagalpur","Muzaffarpur","Purnia","Darbhanga","Bihar Sharif","Arrah","Begusarai","Katihar"]},{name:"Odisha",code:"OR",cities:["Bhubaneswar","Cuttack","Rourkela","Brahmapur","Sambalpur","Puri","Balasore","Bhadrak","Baripada","Jharsuguda"]},{name:"Kerala",code:"KL",cities:["Thiruvananthapuram","Kochi","Kozhikode","Thrissur","Kollam","Palakkad","Alappuzha","Malappuram","Kannur","Kasaragod"]},{name:"Jharkhand",code:"JH",cities:["Ranchi","Jamshedpur","Dhanbad","Bokaro","Deoghar","Phusro","Hazaribagh","Giridih","Ramgarh","Medininagar"]},{name:"Assam",code:"AS",cities:["Guwahati","Silchar","Dibrugarh","Jorhat","Nagaon","Tinsukia","Tezpur","Bongaigaon","Dhubri","North Lakhimpur"]},{name:"Chhattisgarh",code:"CG",cities:["Raipur","Bhilai","Bilaspur","Korba","Durg","Rajnandgaon","Jagdalpur","Raigarh","Ambikapur","Mahasamund"]},{name:"Uttarakhand",code:"UK",cities:["Dehradun","Haridwar","Roorkee","Haldwani","Rudrapur","Kashipur","Rishikesh","Kotdwar","Pithoragarh","Almora"]},{name:"Himachal Pradesh",code:"HP",cities:["Shimla","Dharamshala","Solan","Mandi","Palampur","Baddi","Nahan","Paonta Sahib","Sundarnagar","Chamba"]},{name:"Jammu and Kashmir",code:"JK",cities:["Srinagar","Jammu","Anantnag","Baramulla","Sopore","Kathua","Udhampur","Punch","Rajouri","Kupwara"]},{name:"Goa",code:"GA",cities:["Panaji","Vasco da Gama","Margao","Mapusa","Ponda","Bicholim","Curchorem","Sanquelim","Cuncolim","Quepem"]},{name:"Andhra Pradesh",code:"AP",cities:["Visakhapatnam","Vijayawada","Guntur","Nellore","Kurnool","Rajahmundry","Tirupati","Kakinada","Anantapur","Vizianagaram"]},{name:"Telangana",code:"TS",cities:["Hyderabad","Warangal","Nizamabad","Khammam","Karimnagar","Ramagundam","Mahbubnagar","Nalgonda","Adilabad","Suryapet"]},{name:"Arunachal Pradesh",code:"AR",cities:["Itanagar","Naharlagun","Pasighat","Tezpur","Bomdila","Ziro","Along","Changlang","Tezu","Khonsa"]},{name:"Manipur",code:"MN",cities:["Imphal","Thoubal","Bishnupur","Churachandpur","Ukhrul","Senapati","Tamenglong","Chandel","Jiribam","Kangpokpi"]},{name:"Meghalaya",code:"ML",cities:["Shillong","Tura","Jowai","Nongstoin","Baghmara","Ampati","Resubelpara","Mawkyrwat","Williamnagar","Khliehriat"]},{name:"Mizoram",code:"MZ",cities:["Aizawl","Lunglei","Saiha","Champhai","Kolasib","Serchhip","Mamit","Lawngtlai","Saitual","Khawzawl"]},{name:"Nagaland",code:"NL",cities:["Kohima","Dimapur","Mokokchung","Tuensang","Wokha","Zunheboto","Phek","Kiphire","Longleng","Peren"]},{name:"Sikkim",code:"SK",cities:["Gangtok","Namchi","Geyzing","Mangan","Jorethang","Nayabazar","Rangpo","Singtam","Yuksom","Ravangla"]},{name:"Tripura",code:"TR",cities:["Agartala","Dharmanagar","Udaipur","Kailasahar","Belonia","Khowai","Pratapgarh","Ranir Bazar","Sonamura","Kumarghat"]},{name:"Andaman and Nicobar Islands",code:"AN",cities:["Port Blair","Diglipur","Mayabunder","Rangat","Havelock Island","Neil Island","Car Nicobar","Nancowry","Little Andaman","Baratang"]},{name:"Chandigarh",code:"CH",cities:["Chandigarh"]},{name:"Dadra and Nagar Haveli and Daman and Diu",code:"DN",cities:["Daman","Diu","Silvassa"]},{name:"Lakshadweep",code:"LD",cities:["Kavaratti","Agatti","Minicoy","Amini","Andrott","Kalpeni","Kadmat","Kiltan","Chetlat","Bitra"]},{name:"Puducherry",code:"PY",cities:["Puducherry","Karaikal","Mahe","Yanam"]},{name:"Ladakh",code:"LA",cities:["Leh","Kargil","Nubra","Zanskar","Drass","Khaltse","Nyoma","Durbuk","Khalsi","Turtuk"]}],S=()=>P.map(e=>e.name).sort();function A({selectedState:e,selectedCity:a,onStateChange:r,onCityChange:t,stateError:n,cityError:o}){let[l]=(0,s.useState)(S()),[d,c]=(0,s.useState)([]);return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{children:[i.jsx(j,{htmlFor:"state",children:"State"}),(0,i.jsxs)("select",{id:"state",value:e,onChange:e=>r(e.target.value),className:`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#2c2c27] ${n?"border-red-300":"border-gray-300"}`,children:[i.jsx("option",{value:"",children:"Select State"}),l.map(e=>i.jsx("option",{value:e,children:e},e))]}),n&&i.jsx("p",{className:"text-sm text-red-500 mt-1",children:n})]}),(0,i.jsxs)("div",{children:[i.jsx(j,{htmlFor:"city",children:"City"}),(0,i.jsxs)("select",{id:"city",value:a,onChange:e=>t(e.target.value),disabled:!e||0===d.length,className:`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#2c2c27] ${o?"border-red-300":"border-gray-300"} ${e&&0!==d.length?"":"bg-gray-100 cursor-not-allowed"}`,children:[i.jsx("option",{value:"",children:"Select City"}),d.map(e=>i.jsx("option",{value:e,children:e},e))]}),o&&i.jsx("p",{className:"text-sm text-red-500 mt-1",children:o}),e&&0===d.length&&i.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"No cities available for selected state"})]})]})}function C(){let e=(0,t.useRouter)(),{isAuthenticated:a,isLoading:r}=(0,g.O)(),l=(0,o.rY)(),d=u(),[p,y]=(0,s.useState)(!1),{register:b,handleSubmit:v,watch:P,setValue:S,formState:{errors:C}}=(0,n.cI)({mode:"onChange"});P("pincode");let E=P("state"),R=P("city"),K=(0,s.useCallback)(e=>{S("state",e)},[S]),z=(0,s.useCallback)(e=>{S("city",e)},[S]),M=async e=>{let a={firstName:e.firstName,lastName:e.lastName,address1:e.address1,address2:e.address2,city:e.city,state:e.state,pincode:e.pincode,phone:e.phone};d.setShippingAddress(a)},B=async()=>{if(!d.shippingAddress){d.setError("Please fill in your shipping address");return}if(!d.selectedShipping){d.setError("Shipping cost not calculated. Please enter a valid pincode.");return}if(0===d.cart.length){d.setError("Your cart is empty");return}if(d.finalAmount<=0){d.setError("Invalid order amount");return}y(!0),d.setProcessingPayment(!0),d.setError(null);try{let a="rzp_live_H1Iyl4j48eSFYj";if(!a)throw Error("Payment gateway not configured. Please contact support.");console.log("Creating Razorpay order for amount:",d.finalAmount);let r=await c(d.finalAmount,`order_${Date.now()}_${Math.random().toString(36).substring(2,11)}`,{customer_phone:d.shippingAddress.phone,customer_name:`${d.shippingAddress.firstName} ${d.shippingAddress.lastName}`,shipping_method:d.selectedShipping.name});console.log("Razorpay order created:",r.id),await m({key:a,amount:r.amount,currency:r.currency,name:"Ankkor",description:`Order Payment - ${d.cart.length} item(s)`,order_id:r.id,handler:async a=>{console.log("Payment successful, verifying...",a),d.setError(null);try{let r=await h(a,{address:d.shippingAddress,cartItems:d.cart,shipping:d.selectedShipping});if(console.log("Payment verification result:",r),r.success)l.clearCart(),d.clearCheckout(),e.push(`/order-confirmed?id=${r.orderId}`);else throw Error(r.message||"Payment verification failed")}catch(e){console.error("Payment verification error:",e),d.setError(e instanceof Error?e.message:"Payment verification failed. Please contact support if amount was deducted.")}finally{y(!1),d.setProcessingPayment(!1)}},prefill:{name:`${d.shippingAddress.firstName} ${d.shippingAddress.lastName}`,contact:d.shippingAddress.phone},theme:{color:"#2c2c27"},modal:{ondismiss:()=>{console.log("Payment modal dismissed"),y(!1),d.setProcessingPayment(!1)}}})}catch(a){console.error("Payment error:",a);let e="Payment failed. Please try again.";a.message?.includes("not configured")?e=a.message:a.message?.includes("network")||a.message?.includes("fetch")?e="Network error. Please check your connection and try again.":a.message?.includes("amount")?e="Invalid amount. Please refresh and try again.":a.message&&(e=a.message),d.setError(e)}finally{y(!1),d.setProcessingPayment(!1)}};return r?i.jsx("div",{className:"container mx-auto px-4 py-8",children:(0,i.jsxs)("div",{className:"flex items-center justify-center min-h-[400px]",children:[i.jsx(N.Z,{className:"h-8 w-8 animate-spin"}),i.jsx("span",{className:"ml-2",children:"Loading..."})]})}):a&&0!==l.items.length?(0,i.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[i.jsx("h1",{className:"text-3xl font-serif mb-8",children:"Checkout"}),d.error&&i.jsx("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded",children:d.error}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,i.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,i.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm",children:[(0,i.jsxs)("h2",{className:"text-xl font-medium mb-4 flex items-center",children:[i.jsx(w.Z,{className:"mr-2 h-5 w-5"}),"Shipping Address"]}),(0,i.jsxs)("form",{onSubmit:v(M),className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[i.jsx(j,{htmlFor:"firstName",children:"First Name"}),i.jsx(x.I,{id:"firstName",...b("firstName",{required:"First name is required"}),className:C.firstName?"border-red-300":""}),C.firstName&&i.jsx("p",{className:"text-sm text-red-500 mt-1",children:C.firstName.message})]}),(0,i.jsxs)("div",{children:[i.jsx(j,{htmlFor:"lastName",children:"Last Name"}),i.jsx(x.I,{id:"lastName",...b("lastName",{required:"Last name is required"}),className:C.lastName?"border-red-300":""}),C.lastName&&i.jsx("p",{className:"text-sm text-red-500 mt-1",children:C.lastName.message})]}),(0,i.jsxs)("div",{className:"md:col-span-2",children:[i.jsx(j,{htmlFor:"address1",children:"Address Line 1"}),i.jsx(x.I,{id:"address1",...b("address1",{required:"Address is required"}),className:C.address1?"border-red-300":""}),C.address1&&i.jsx("p",{className:"text-sm text-red-500 mt-1",children:C.address1.message})]}),(0,i.jsxs)("div",{className:"md:col-span-2",children:[i.jsx(j,{htmlFor:"address2",children:"Address Line 2 (Optional)"}),i.jsx(x.I,{id:"address2",...b("address2")})]}),i.jsx(A,{selectedState:E||"",selectedCity:R||"",onStateChange:K,onCityChange:z,stateError:C.state?.message,cityError:C.city?.message}),(0,i.jsxs)("div",{children:[i.jsx(j,{htmlFor:"pincode",children:"Pincode"}),i.jsx(x.I,{id:"pincode",...b("pincode",{required:"Pincode is required",pattern:{value:/^[0-9]{6}$/,message:"Please enter a valid 6-digit pincode"}}),className:C.pincode?"border-red-300":"",placeholder:"Enter 6-digit pincode"}),C.pincode&&i.jsx("p",{className:"text-sm text-red-500 mt-1",children:C.pincode.message})]}),(0,i.jsxs)("div",{children:[i.jsx(j,{htmlFor:"phone",children:"Phone Number"}),i.jsx(x.I,{id:"phone",...b("phone",{required:"Phone number is required"}),className:C.phone?"border-red-300":""}),C.phone&&i.jsx("p",{className:"text-sm text-red-500 mt-1",children:C.phone.message})]})]}),i.jsx(f.z,{type:"submit",className:"mt-4 w-full bg-[#2c2c27] hover:bg-[#3c3c37] text-white",children:"Save Address & Continue"})]})]}),(0,i.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm",children:[(0,i.jsxs)("h2",{className:"text-xl font-medium mb-4 flex items-center",children:[i.jsx(w.Z,{className:"mr-2 h-5 w-5"}),"Shipping Information"]}),i.jsx("div",{className:"mb-4 p-3 bg-green-50 border border-green-200 rounded-lg",children:(0,i.jsxs)("p",{className:"text-sm text-green-700",children:["\uD83D\uDE9A Free shipping on orders above ₹2999",d.subtotal>0&&d.subtotal<=2999&&(0,i.jsxs)("span",{className:"ml-2 text-green-600",children:["(Add ₹",(2999-d.subtotal+1).toFixed(0)," more for free shipping)"]})]})}),E?d.isLoadingShipping?(0,i.jsxs)("div",{className:"flex items-center justify-center py-8",children:[i.jsx(N.Z,{className:"h-6 w-6 animate-spin mr-2"}),i.jsx("span",{children:"Calculating shipping cost..."})]}):d.selectedShipping?i.jsx("div",{className:"border rounded-lg p-4 bg-gray-50",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[i.jsx("h3",{className:"font-medium",children:"Standard Shipping"}),i.jsx("p",{className:"text-sm text-gray-500",children:"Estimated delivery: 5-7 days"})]}),i.jsx("div",{className:"text-lg font-medium",children:0===d.selectedShipping.cost?"Free":`₹${d.selectedShipping.cost.toFixed(2)}`})]})}):i.jsx("div",{className:"text-gray-500 py-4",children:"Unable to calculate shipping for this address"}):i.jsx("div",{className:"text-gray-500 py-4",children:"Please select a state to see shipping options"})]}),(0,i.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm",children:[(0,i.jsxs)("h2",{className:"text-xl font-medium mb-4 flex items-center",children:[i.jsx(k.Z,{className:"mr-2 h-5 w-5"}),"Payment"]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center p-4 border rounded-lg",children:[i.jsx("input",{type:"radio",id:"razorpay",name:"payment",checked:!0,readOnly:!0,className:"mr-3"}),(0,i.jsxs)("div",{children:[i.jsx("label",{htmlFor:"razorpay",className:"font-medium",children:"Razorpay"}),i.jsx("p",{className:"text-sm text-gray-600",children:"Pay securely with credit card, debit card, UPI, or net banking"})]})]}),i.jsx(f.z,{onClick:B,className:"w-full py-6 bg-[#2c2c27] hover:bg-[#3c3c37] text-white",disabled:p||!d.shippingAddress||!d.selectedShipping||d.isProcessingPayment,children:p||d.isProcessingPayment?(0,i.jsxs)(i.Fragment,{children:[i.jsx(N.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing Payment..."]}):`Proceed to Pay - ₹${d.finalAmount.toFixed(2)}`})]})]})]}),i.jsx("div",{className:"lg:col-span-1",children:(0,i.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm sticky top-8",children:[i.jsx("h2",{className:"text-xl font-medium mb-4",children:"Order Summary"}),(0,i.jsxs)("div",{className:"space-y-4",children:[d.cart.map(e=>(0,i.jsxs)("div",{className:"flex gap-4 py-2 border-b",children:[e.image?.url&&i.jsx("div",{className:"relative h-16 w-16 bg-gray-100 flex-shrink-0",children:i.jsx("img",{src:e.image.url,alt:e.name,className:"h-full w-full object-cover rounded"})}),(0,i.jsxs)("div",{className:"flex-1",children:[i.jsx("h3",{className:"text-sm font-medium",children:e.name}),(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:["₹","string"==typeof e.price?parseFloat(e.price).toFixed(2):e.price.toFixed(2)," \xd7 ",e.quantity]})]}),(0,i.jsxs)("div",{className:"text-right",children:["₹",("string"==typeof e.price?parseFloat(e.price)*e.quantity:e.price*e.quantity).toFixed(2)]})]},e.id)),(0,i.jsxs)("div",{className:"pt-4 space-y-2",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-gray-600",children:"Subtotal"}),(0,i.jsxs)("span",{children:["₹",d.subtotal.toFixed(2)]})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-gray-600",children:"Shipping"}),i.jsx("span",{className:"flex items-center gap-1",children:E?d.isLoadingShipping?(0,i.jsxs)(i.Fragment,{children:[i.jsx(N.Z,{className:"h-3 w-3 animate-spin"}),i.jsx("span",{className:"text-sm",children:"Updating..."})]}):d.selectedShipping?0===d.selectedShipping.cost?"Free":`₹${d.selectedShipping.cost.toFixed(2)}`:"Calculating...":"Select state"})]}),(0,i.jsxs)("div",{className:"flex justify-between text-lg font-medium pt-2 border-t",children:[i.jsx("span",{children:"Total"}),(0,i.jsxs)("span",{children:["₹",d.finalAmount.toFixed(2)]})]})]})]})]})})]})]}):null}},72248:(e,a,r)=>{"use strict";let i,s;r.d(a,{z:()=>m});var t=r(10326);r(17577);var n=r(34214),o=r(41135);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,d=o.W;var c=r(51223);let h=(i="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s={variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}},e=>{var a;if((null==s?void 0:s.variants)==null)return d(i,null==e?void 0:e.class,null==e?void 0:e.className);let{variants:r,defaultVariants:t}=s,n=Object.keys(r).map(a=>{let i=null==e?void 0:e[a],s=null==t?void 0:t[a];if(null===i)return null;let n=l(i)||l(s);return r[a][n]}),o=e&&Object.entries(e).reduce((e,a)=>{let[r,i]=a;return void 0===i||(e[r]=i),e},{});return d(i,n,null==s?void 0:null===(a=s.compoundVariants)||void 0===a?void 0:a.reduce((e,a)=>{let{class:r,className:i,...s}=a;return Object.entries(s).every(e=>{let[a,r]=e;return Array.isArray(r)?r.includes({...t,...o}[a]):({...t,...o})[a]===r})?[...e,r,i]:e},[]),null==e?void 0:e.class,null==e?void 0:e.className)});function m({className:e,variant:a,size:r,asChild:i=!1,...s}){let o=i?n.g7:"button";return t.jsx(o,{"data-slot":"button",className:(0,c.cn)(h({variant:a,size:r,className:e})),...s})}},41190:(e,a,r)=>{"use strict";r.d(a,{I:()=>n});var i=r(10326),s=r(17577),t=r(51223);let n=s.forwardRef(({className:e,type:a,...r},s)=>i.jsx("input",{type:a,"data-slot":"input",className:(0,t.cn)("border-[#e5e2d9] file:text-[#2c2c27] placeholder:text-[#8a8778] selection:bg-[#2c2c27] selection:text-[#f4f3f0] flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-[#8a8778] focus-visible:ring-[#8a8778]/50 focus-visible:ring-[3px]","aria-invalid:ring-[#ff4d4f]/20 dark:aria-invalid:ring-[#ff4d4f]/40 aria-invalid:border-[#ff4d4f]",e),ref:s,...r}));n.displayName="Input"},51223:(e,a,r)=>{"use strict";r.d(a,{cn:()=>t});var i=r(41135),s=r(31009);function t(...e){return(0,s.m6)((0,i.W)(e))}},51324:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>i});let i=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\checkout\page.tsx#default`)},48051:(e,a,r)=>{"use strict";r.d(a,{F:()=>t,e:()=>n});var i=r(17577);function s(e,a){if("function"==typeof e)return e(a);null!=e&&(e.current=a)}function t(...e){return a=>{let r=!1,i=e.map(e=>{let i=s(e,a);return r||"function"!=typeof i||(r=!0),i});if(r)return()=>{for(let a=0;a<i.length;a++){let r=i[a];"function"==typeof r?r():s(e[a],null)}}}}function n(...e){return i.useCallback(t(...e),e)}},45226:(e,a,r)=>{"use strict";r.d(a,{WV:()=>n});var i=r(17577);r(60962);var s=r(34214),t=r(10326),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,a)=>{let r=i.forwardRef((e,r)=>{let{asChild:i,...n}=e,o=i?s.g7:a;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,t.jsx)(o,{...n,ref:r})});return r.displayName=`Primitive.${a}`,{...e,[a]:r}},{})},34214:(e,a,r)=>{"use strict";r.d(a,{g7:()=>n});var i=r(17577),s=r(48051),t=r(10326),n=i.forwardRef((e,a)=>{let{children:r,...s}=e,n=i.Children.toArray(r),l=n.find(d);if(l){let e=l.props.children,r=n.map(a=>a!==l?a:i.Children.count(e)>1?i.Children.only(null):i.isValidElement(e)?e.props.children:null);return(0,t.jsx)(o,{...s,ref:a,children:i.isValidElement(e)?i.cloneElement(e,void 0,r):null})}return(0,t.jsx)(o,{...s,ref:a,children:r})});n.displayName="Slot";var o=i.forwardRef((e,a)=>{let{children:r,...t}=e;if(i.isValidElement(r)){let e=function(e){let a=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=a&&"isReactWarning"in a&&a.isReactWarning;return r?e.ref:(r=(a=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),n=function(e,a){let r={...a};for(let i in a){let s=e[i],t=a[i];/^on[A-Z]/.test(i)?s&&t?r[i]=(...e)=>{t(...e),s(...e)}:s&&(r[i]=s):"style"===i?r[i]={...s,...t}:"className"===i&&(r[i]=[s,t].filter(Boolean).join(" "))}return{...e,...r}}(t,r.props);return r.type!==i.Fragment&&(n.ref=a?(0,s.F)(a,e):e),i.cloneElement(r,n)}return i.Children.count(r)>1?i.Children.only(null):null});o.displayName="SlotClone";var l=({children:e})=>(0,t.jsx)(t.Fragment,{children:e});function d(e){return i.isValidElement(e)&&e.type===l}}};var a=require("../../webpack-runtime.js");a.C(e);var r=e=>a(a.s=e),i=a.X(0,[8948,1057,325,5010,5436],()=>r(824));module.exports=i})();