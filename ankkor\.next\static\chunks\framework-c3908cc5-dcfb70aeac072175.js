"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6271],{29668:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{createKey:function(){return q},default:function(){return X},matchesMiddleware:function(){return I}});let r=a(38754),s=a(61757),i=a(33575),o=a(32856),l=a(95026),n=s._(a(80676)),h=a(75876),c=a(91623),p=r._(a(58967)),u=a(45782),d=a(31735),f=a(62757);a(72431);let m=a(43323),_=a(36309),g=a(5058);a(97193);let P=a(80626),v=a(28878),w=a(14509),y=a(91566),S=a(41412),R=a(71838),b=a(64813),C=a(79423),L=a(58754),E=a(15604),A=a(9012),N=a(65853),T=a(6312),x=a(12795),D=a(37399),H=a(12179);function B(){return Object.assign(Error("Route Cancelled"),{cancelled:!0})}async function I(e){let t=await Promise.resolve(e.router.pageLoader.getMiddleware());if(!t)return!1;let{pathname:a}=(0,P.parsePath)(e.asPath),r=(0,R.hasBasePath)(a)?(0,y.removeBasePath)(a):a,s=(0,S.addBasePath)((0,v.addLocale)(r,e.locale));return t.some(e=>new RegExp(e.regexp).test(s))}function j(e){let t=(0,u.getLocationOrigin)();return e.startsWith(t)?e.substring(t.length):e}function k(e,t,a){let[r,s]=(0,b.resolveHref)(e,t,!0),i=(0,u.getLocationOrigin)(),o=r.startsWith(i),l=s&&s.startsWith(i);r=j(r),s=s?j(s):s;let n=o?r:(0,S.addBasePath)(r),h=a?j((0,b.resolveHref)(e,a)):s||r;return{url:n,as:l?h:(0,S.addBasePath)(h)}}function U(e,t){let a=(0,i.removeTrailingSlash)((0,h.denormalizePagePath)(e));return"/404"===a||"/_error"===a?e:(t.includes(a)||t.some(t=>{if((0,d.isDynamicRoute)(t)&&(0,_.getRouteRegex)(t).re.test(a))return e=t,!0}),(0,i.removeTrailingSlash)(e))}async function O(e){if(!await I(e)||!e.fetchData)return null;let t=await e.fetchData(),a=await function(e,t,a){let r={basePath:a.router.basePath,i18n:{locales:a.router.locales},trailingSlash:!1},s=t.headers.get("x-nextjs-rewrite"),l=s||t.headers.get("x-nextjs-matched-path"),n=t.headers.get("x-matched-path");if(!n||l||n.includes("__next_data_catchall")||n.includes("/_error")||n.includes("/404")||(l=n),l){if(l.startsWith("/")){let t=(0,f.parseRelativeUrl)(l),n=(0,L.getNextPathnameInfo)(t.pathname,{nextConfig:r,parseData:!0}),h=(0,i.removeTrailingSlash)(n.pathname);return Promise.all([a.router.pageLoader.getPageList(),(0,o.getClientBuildManifest)()]).then(i=>{let[o,{__rewrites:l}]=i,p=(0,v.addLocale)(n.pathname,n.locale);if((0,d.isDynamicRoute)(p)||!s&&o.includes((0,c.normalizeLocalePath)((0,y.removeBasePath)(p),a.router.locales).pathname)){let a=(0,L.getNextPathnameInfo)((0,f.parseRelativeUrl)(e).pathname,{nextConfig:r,parseData:!0});p=(0,S.addBasePath)(a.pathname),t.pathname=p}if(!o.includes(h)){let e=U(h,o);e!==h&&(h=e)}let u=o.includes(h)?h:U((0,c.normalizeLocalePath)((0,y.removeBasePath)(t.pathname),a.router.locales).pathname,o);if((0,d.isDynamicRoute)(u)){let e=(0,m.getRouteMatcher)((0,_.getRouteRegex)(u))(p);Object.assign(t.query,e||{})}return{type:"rewrite",parsedAs:t,resolvedHref:u}})}let t=(0,P.parsePath)(e);return Promise.resolve({type:"redirect-external",destination:""+(0,E.formatNextPathnameInfo)({...(0,L.getNextPathnameInfo)(t.pathname,{nextConfig:r,parseData:!0}),defaultLocale:a.router.defaultLocale,buildId:""})+t.query+t.hash})}let h=t.headers.get("x-nextjs-redirect");if(h){if(h.startsWith("/")){let e=(0,P.parsePath)(h),t=(0,E.formatNextPathnameInfo)({...(0,L.getNextPathnameInfo)(e.pathname,{nextConfig:r,parseData:!0}),defaultLocale:a.router.defaultLocale,buildId:""});return Promise.resolve({type:"redirect-internal",newAs:""+t+e.query+e.hash,newUrl:""+t+e.query+e.hash})}return Promise.resolve({type:"redirect-external",destination:h})}return Promise.resolve({type:"next"})}(t.dataHref,t.response,e);return{dataHref:t.dataHref,json:t.json,response:t.response,text:t.text,cacheKey:t.cacheKey,effect:a}}let M=Symbol("SSG_DATA_NOT_FOUND");function F(e){try{return JSON.parse(e)}catch(e){return null}}function W(e){let{dataHref:t,inflightCache:a,isPrefetch:r,hasMiddleware:s,isServerRender:i,parseJSON:l,persistCache:n,isBackground:h,unstable_skipClientCache:c}=e,{href:p}=new URL(t,window.location.href),u=e=>{var h;return(function e(t,a,r){return fetch(t,{credentials:"same-origin",method:r.method||"GET",headers:Object.assign({},r.headers,{"x-nextjs-data":"1"})}).then(s=>!s.ok&&a>1&&s.status>=500?e(t,a-1,r):s)})(t,i?3:1,{headers:Object.assign({},r?{purpose:"prefetch"}:{},r&&s?{"x-middleware-prefetch":"1"}:{}),method:null!=(h=null==e?void 0:e.method)?h:"GET"}).then(a=>a.ok&&(null==e?void 0:e.method)==="HEAD"?{dataHref:t,response:a,text:"",json:{},cacheKey:p}:a.text().then(e=>{if(!a.ok){if(s&&[301,302,307,308].includes(a.status))return{dataHref:t,response:a,text:e,json:{},cacheKey:p};if(404===a.status){var r;if(null==(r=F(e))?void 0:r.notFound)return{dataHref:t,json:{notFound:M},response:a,text:e,cacheKey:p}}let l=Error("Failed to load static props");throw i||(0,o.markAssetError)(l),l}return{dataHref:t,json:l?F(e):null,response:a,text:e,cacheKey:p}})).then(e=>(n&&"no-cache"!==e.response.headers.get("x-middleware-cache")||delete a[p],e)).catch(e=>{throw c||delete a[p],("Failed to fetch"===e.message||"NetworkError when attempting to fetch resource."===e.message||"Load failed"===e.message)&&(0,o.markAssetError)(e),e})};return c&&n?u({}).then(e=>("no-cache"!==e.response.headers.get("x-middleware-cache")&&(a[p]=Promise.resolve(e)),e)):void 0!==a[p]?a[p]:a[p]=u(h?{method:"HEAD"}:{})}function q(){return Math.random().toString(36).slice(2,10)}function G(e){let{url:t,router:a}=e;if(t===(0,S.addBasePath)((0,v.addLocale)(a.asPath,a.locale)))throw Error("Invariant: attempted to hard navigate to the same URL "+t+" "+location.href);window.location.href=t}let V=e=>{let{route:t,router:a}=e,r=!1,s=a.clc=()=>{r=!0};return()=>{if(r){let e=Error('Abort fetching component for route: "'+t+'"');throw e.cancelled=!0,e}s===a.clc&&(a.clc=null)}};class X{reload(){window.location.reload()}back(){window.history.back()}forward(){window.history.forward()}push(e,t,a){return void 0===a&&(a={}),{url:e,as:t}=k(this,e,t),this.change("pushState",e,t,a)}replace(e,t,a){return void 0===a&&(a={}),{url:e,as:t}=k(this,e,t),this.change("replaceState",e,t,a)}async _bfl(e,t,a,r){{let n=!1,h=!1;for(let c of[e,t])if(c){let t=(0,i.removeTrailingSlash)(new URL(c,"http://n").pathname),p=(0,S.addBasePath)((0,v.addLocale)(t,a||this.locale));if(t!==(0,i.removeTrailingSlash)(new URL(this.asPath,"http://n").pathname)){var s,o,l;for(let e of(n=n||!!(null==(s=this._bfl_s)?void 0:s.contains(t))||!!(null==(o=this._bfl_s)?void 0:o.contains(p)),[t,p])){let t=e.split("/");for(let e=0;!h&&e<t.length+1;e++){let a=t.slice(0,e).join("/");if(a&&(null==(l=this._bfl_d)?void 0:l.contains(a))){h=!0;break}}}if(n||h){if(r)return!0;return G({url:(0,S.addBasePath)((0,v.addLocale)(e,a||this.locale,this.defaultLocale)),router:this}),new Promise(()=>{})}}}}return!1}async change(e,t,a,r,s){var h,c,p,b,C,L,E,T,H;let j,O;if(!(0,N.isLocalURL)(t))return G({url:t,router:this}),!1;let F=1===r._h;F||r.shallow||await this._bfl(a,void 0,r.locale);let W=F||r._shouldResolveHref||(0,P.parsePath)(t).pathname===(0,P.parsePath)(a).pathname,q={...this.state},V=!0!==this.isReady;this.isReady=!0;let K=this.isSsr;if(F||(this.isSsr=!1),F&&this.clc)return!1;let J=q.locale;u.ST&&performance.mark("routeChange");let{shallow:z=!1,scroll:Q=!0}=r,$={shallow:z};this._inFlightRoute&&this.clc&&(K||X.events.emit("routeChangeError",B(),this._inFlightRoute,$),this.clc(),this.clc=null),a=(0,S.addBasePath)((0,v.addLocale)((0,R.hasBasePath)(a)?(0,y.removeBasePath)(a):a,r.locale,this.defaultLocale));let Y=(0,w.removeLocale)((0,R.hasBasePath)(a)?(0,y.removeBasePath)(a):a,q.locale);this._inFlightRoute=a;let Z=J!==q.locale;if(!F&&this.onlyAHashChange(Y)&&!Z){q.asPath=Y,X.events.emit("hashChangeStart",a,$),this.changeState(e,t,a,{...r,scroll:!1}),Q&&this.scrollToHash(Y);try{await this.set(q,this.components[q.route],null)}catch(e){throw(0,n.default)(e)&&e.cancelled&&X.events.emit("routeChangeError",e,Y,$),e}return X.events.emit("hashChangeComplete",a,$),!0}let ee=(0,f.parseRelativeUrl)(t),{pathname:et,query:ea}=ee;try{[j,{__rewrites:O}]=await Promise.all([this.pageLoader.getPageList(),(0,o.getClientBuildManifest)(),this.pageLoader.getMiddleware()])}catch(e){return G({url:a,router:this}),!1}this.urlIsNew(Y)||Z||(e="replaceState");let er=a;et=et?(0,i.removeTrailingSlash)((0,y.removeBasePath)(et)):et;let es=(0,i.removeTrailingSlash)(et),ei=a.startsWith("/")&&(0,f.parseRelativeUrl)(a).pathname;if(null==(h=this.components[et])?void 0:h.__appRouter)return G({url:a,router:this}),new Promise(()=>{});let eo=!!(ei&&es!==ei&&(!(0,d.isDynamicRoute)(es)||!(0,m.getRouteMatcher)((0,_.getRouteRegex)(es))(ei))),el=!r.shallow&&await I({asPath:a,locale:q.locale,router:this});if(F&&el&&(W=!1),W&&"/_error"!==et&&(r._shouldResolveHref=!0,ee.pathname=U(et,j),ee.pathname===et||(et=ee.pathname,ee.pathname=(0,S.addBasePath)(et),el||(t=(0,g.formatWithValidation)(ee)))),!(0,N.isLocalURL)(a))return G({url:a,router:this}),!1;er=(0,w.removeLocale)((0,y.removeBasePath)(er),q.locale),es=(0,i.removeTrailingSlash)(et);let en=!1;if((0,d.isDynamicRoute)(es)){let e=(0,f.parseRelativeUrl)(er),r=e.pathname,s=(0,_.getRouteRegex)(es);en=(0,m.getRouteMatcher)(s)(r);let i=es===r,o=i?(0,D.interpolateAs)(es,r,ea):{};if(en&&(!i||o.result))i?a=(0,g.formatWithValidation)(Object.assign({},e,{pathname:o.result,query:(0,x.omit)(ea,o.params)})):Object.assign(ea,en);else{let e=Object.keys(s.groups).filter(e=>!ea[e]&&!s.groups[e].optional);if(e.length>0&&!el)throw Error((i?"The provided `href` ("+t+") value is missing query values ("+e.join(", ")+") to be interpolated properly. ":"The provided `as` value ("+r+") is incompatible with the `href` value ("+es+"). ")+"Read more: https://nextjs.org/docs/messages/"+(i?"href-interpolation-failed":"incompatible-href-as"))}}F||X.events.emit("routeChangeStart",a,$);let eh="/404"===this.pathname||"/_error"===this.pathname;try{let i=await this.getRouteInfo({route:es,pathname:et,query:ea,as:a,resolvedAs:er,routeProps:$,locale:q.locale,isPreview:q.isPreview,hasMiddleware:el,unstable_skipClientCache:r.unstable_skipClientCache,isQueryUpdating:F&&!this.isFallback,isMiddlewareRewrite:eo});if(F||r.shallow||await this._bfl(a,"resolvedAs"in i?i.resolvedAs:void 0,q.locale),"route"in i&&el){es=et=i.route||es,$.shallow||(ea=Object.assign({},i.query||{},ea));let e=(0,R.hasBasePath)(ee.pathname)?(0,y.removeBasePath)(ee.pathname):ee.pathname;if(en&&et!==e&&Object.keys(en).forEach(e=>{en&&ea[e]===en[e]&&delete ea[e]}),(0,d.isDynamicRoute)(et)){let e=!$.shallow&&i.resolvedAs?i.resolvedAs:(0,S.addBasePath)((0,v.addLocale)(new URL(a,location.href).pathname,q.locale),!0);(0,R.hasBasePath)(e)&&(e=(0,y.removeBasePath)(e));let t=(0,_.getRouteRegex)(et),r=(0,m.getRouteMatcher)(t)(new URL(e,location.href).pathname);r&&Object.assign(ea,r)}}if("type"in i){if("redirect-internal"===i.type)return this.change(e,i.newUrl,i.newAs,r);return G({url:i.destination,router:this}),new Promise(()=>{})}let o=i.Component;if(o&&o.unstable_scriptLoader&&[].concat(o.unstable_scriptLoader()).forEach(e=>{(0,l.handleClientScriptLoad)(e.props)}),(i.__N_SSG||i.__N_SSP)&&i.props){if(i.props.pageProps&&i.props.pageProps.__N_REDIRECT){r.locale=!1;let t=i.props.pageProps.__N_REDIRECT;if(t.startsWith("/")&&!1!==i.props.pageProps.__N_REDIRECT_BASE_PATH){let a=(0,f.parseRelativeUrl)(t);a.pathname=U(a.pathname,j);let{url:s,as:i}=k(this,t,t);return this.change(e,s,i,r)}return G({url:t,router:this}),new Promise(()=>{})}if(q.isPreview=!!i.props.__N_PREVIEW,i.props.notFound===M){let e;try{await this.fetchComponent("/404"),e="/404"}catch(t){e="/_error"}if(i=await this.getRouteInfo({route:e,pathname:e,query:ea,as:a,resolvedAs:er,routeProps:{shallow:!1},locale:q.locale,isPreview:q.isPreview,isNotFound:!0}),"type"in i)throw Error("Unexpected middleware effect on /404")}}F&&"/_error"===this.pathname&&(null==(p=self.__NEXT_DATA__.props)?void 0:null==(c=p.pageProps)?void 0:c.statusCode)===500&&(null==(b=i.props)?void 0:b.pageProps)&&(i.props.pageProps.statusCode=500);let h=r.shallow&&q.route===(null!=(C=i.route)?C:es),u=null!=(L=r.scroll)?L:!F&&!h,g=null!=s?s:u?{x:0,y:0}:null,P={...q,route:es,pathname:et,query:ea,asPath:Y,isFallback:!1};if(F&&eh){if(i=await this.getRouteInfo({route:this.pathname,pathname:this.pathname,query:ea,as:a,resolvedAs:er,routeProps:{shallow:!1},locale:q.locale,isPreview:q.isPreview,isQueryUpdating:F&&!this.isFallback}),"type"in i)throw Error("Unexpected middleware effect on "+this.pathname);"/_error"===this.pathname&&(null==(T=self.__NEXT_DATA__.props)?void 0:null==(E=T.pageProps)?void 0:E.statusCode)===500&&(null==(H=i.props)?void 0:H.pageProps)&&(i.props.pageProps.statusCode=500);try{await this.set(P,i,g)}catch(e){throw(0,n.default)(e)&&e.cancelled&&X.events.emit("routeChangeError",e,Y,$),e}return!0}if(X.events.emit("beforeHistoryChange",a,$),this.changeState(e,t,a,r),!(F&&!g&&!V&&!Z&&(0,A.compareRouterStates)(P,this.state))){try{await this.set(P,i,g)}catch(e){if(e.cancelled)i.error=i.error||e;else throw e}if(i.error)throw F||X.events.emit("routeChangeError",i.error,Y,$),i.error;F||X.events.emit("routeChangeComplete",a,$),u&&/#.+$/.test(a)&&this.scrollToHash(a)}return!0}catch(e){if((0,n.default)(e)&&e.cancelled)return!1;throw e}}changeState(e,t,a,r){void 0===r&&(r={}),("pushState"!==e||(0,u.getURL)()!==a)&&(this._shallow=r.shallow,window.history[e]({url:t,as:a,options:r,__N:!0,key:this._key="pushState"!==e?this._key:q()},"",a))}async handleRouteInfoError(e,t,a,r,s,i){if(console.error(e),e.cancelled)throw e;if((0,o.isAssetError)(e)||i)throw X.events.emit("routeChangeError",e,r,s),G({url:r,router:this}),B();try{let r;let{page:s,styleSheets:i}=await this.fetchComponent("/_error"),o={props:r,Component:s,styleSheets:i,err:e,error:e};if(!o.props)try{o.props=await this.getInitialProps(s,{err:e,pathname:t,query:a})}catch(e){console.error("Error in error page `getInitialProps`: ",e),o.props={}}return o}catch(e){return this.handleRouteInfoError((0,n.default)(e)?e:Error(e+""),t,a,r,s,!0)}}async getRouteInfo(e){let{route:t,pathname:a,query:r,as:s,resolvedAs:o,routeProps:l,locale:h,hasMiddleware:p,isPreview:u,unstable_skipClientCache:d,isQueryUpdating:f,isMiddlewareRewrite:m,isNotFound:_}=e,P=t;try{var v,w,S,R;let e=this.components[P];if(l.shallow&&e&&this.route===P)return e;let t=V({route:P,router:this});p&&(e=void 0);let n=!e||"initial"in e?void 0:e,b={dataHref:this.pageLoader.getDataHref({href:(0,g.formatWithValidation)({pathname:a,query:r}),skipInterpolation:!0,asPath:_?"/404":o,locale:h}),hasMiddleware:!0,isServerRender:this.isSsr,parseJSON:!0,inflightCache:f?this.sbc:this.sdc,persistCache:!u,isPrefetch:!1,unstable_skipClientCache:d,isBackground:f},L=f&&!m?null:await O({fetchData:()=>W(b),asPath:_?"/404":o,locale:h,router:this}).catch(e=>{if(f)return null;throw e});if(L&&("/_error"===a||"/404"===a)&&(L.effect=void 0),f&&(L?L.json=self.__NEXT_DATA__.props:L={json:self.__NEXT_DATA__.props}),t(),(null==L?void 0:null==(v=L.effect)?void 0:v.type)==="redirect-internal"||(null==L?void 0:null==(w=L.effect)?void 0:w.type)==="redirect-external")return L.effect;if((null==L?void 0:null==(S=L.effect)?void 0:S.type)==="rewrite"){let t=(0,i.removeTrailingSlash)(L.effect.resolvedHref),s=await this.pageLoader.getPageList();if((!f||s.includes(t))&&(P=t,a=L.effect.resolvedHref,r={...r,...L.effect.parsedAs.query},o=(0,y.removeBasePath)((0,c.normalizeLocalePath)(L.effect.parsedAs.pathname,this.locales).pathname),e=this.components[P],l.shallow&&e&&this.route===P&&!p))return{...e,route:P}}if((0,C.isAPIRoute)(P))return G({url:s,router:this}),new Promise(()=>{});let E=n||await this.fetchComponent(P).then(e=>({Component:e.page,styleSheets:e.styleSheets,__N_SSG:e.mod.__N_SSG,__N_SSP:e.mod.__N_SSP})),A=null==L?void 0:null==(R=L.response)?void 0:R.headers.get("x-middleware-skip"),N=E.__N_SSG||E.__N_SSP;A&&(null==L?void 0:L.dataHref)&&delete this.sdc[L.dataHref];let{props:T,cacheKey:x}=await this._getData(async()=>{if(N){if((null==L?void 0:L.json)&&!A)return{cacheKey:L.cacheKey,props:L.json};let e=(null==L?void 0:L.dataHref)?L.dataHref:this.pageLoader.getDataHref({href:(0,g.formatWithValidation)({pathname:a,query:r}),asPath:o,locale:h}),t=await W({dataHref:e,isServerRender:this.isSsr,parseJSON:!0,inflightCache:A?{}:this.sdc,persistCache:!u,isPrefetch:!1,unstable_skipClientCache:d});return{cacheKey:t.cacheKey,props:t.json||{}}}return{headers:{},props:await this.getInitialProps(E.Component,{pathname:a,query:r,asPath:s,locale:h,locales:this.locales,defaultLocale:this.defaultLocale})}});return E.__N_SSP&&b.dataHref&&x&&delete this.sdc[x],this.isPreview||!E.__N_SSG||f||W(Object.assign({},b,{isBackground:!0,persistCache:!1,inflightCache:this.sbc})).catch(()=>{}),T.pageProps=Object.assign({},T.pageProps),E.props=T,E.route=P,E.query=r,E.resolvedAs=o,this.components[P]=E,E}catch(e){return this.handleRouteInfoError((0,n.getProperError)(e),a,r,s,l)}}set(e,t,a){return this.state=e,this.sub(t,this.components["/_app"].Component,a)}beforePopState(e){this._bps=e}onlyAHashChange(e){if(!this.asPath)return!1;let[t,a]=this.asPath.split("#",2),[r,s]=e.split("#",2);return!!s&&t===r&&a===s||t===r&&a!==s}scrollToHash(e){let[,t=""]=e.split("#",2);(0,H.handleSmoothScroll)(()=>{if(""===t||"top"===t){window.scrollTo(0,0);return}let e=decodeURIComponent(t),a=document.getElementById(e);if(a){a.scrollIntoView();return}let r=document.getElementsByName(e)[0];r&&r.scrollIntoView()},{onlyHashChange:this.onlyAHashChange(e)})}urlIsNew(e){return this.asPath!==e}async prefetch(e,t,a){if(void 0===t&&(t=e),void 0===a&&(a={}),(0,T.isBot)(window.navigator.userAgent))return;let r=(0,f.parseRelativeUrl)(e),s=r.pathname,{pathname:o,query:l}=r,n=o,h=await this.pageLoader.getPageList(),c=t,p=void 0!==a.locale?a.locale||void 0:this.locale,u=await I({asPath:t,locale:p,router:this});r.pathname=U(r.pathname,h),(0,d.isDynamicRoute)(r.pathname)&&(o=r.pathname,r.pathname=o,Object.assign(l,(0,m.getRouteMatcher)((0,_.getRouteRegex)(r.pathname))((0,P.parsePath)(t).pathname)||{}),u||(e=(0,g.formatWithValidation)(r)));let v=await O({fetchData:()=>W({dataHref:this.pageLoader.getDataHref({href:(0,g.formatWithValidation)({pathname:n,query:l}),skipInterpolation:!0,asPath:c,locale:p}),hasMiddleware:!0,isServerRender:!1,parseJSON:!0,inflightCache:this.sdc,persistCache:!this.isPreview,isPrefetch:!0}),asPath:t,locale:p,router:this});if((null==v?void 0:v.effect.type)==="rewrite"&&(r.pathname=v.effect.resolvedHref,o=v.effect.resolvedHref,l={...l,...v.effect.parsedAs.query},c=v.effect.parsedAs.pathname,e=(0,g.formatWithValidation)(r)),(null==v?void 0:v.effect.type)==="redirect-external")return;let w=(0,i.removeTrailingSlash)(o);await this._bfl(t,c,a.locale,!0)&&(this.components[s]={__appRouter:!0}),await Promise.all([this.pageLoader._isSsg(w).then(t=>!!t&&W({dataHref:(null==v?void 0:v.json)?null==v?void 0:v.dataHref:this.pageLoader.getDataHref({href:e,asPath:c,locale:p}),isServerRender:!1,parseJSON:!0,inflightCache:this.sdc,persistCache:!this.isPreview,isPrefetch:!0,unstable_skipClientCache:a.unstable_skipClientCache||a.priority&&!0}).then(()=>!1).catch(()=>!1)),this.pageLoader[a.priority?"loadPage":"prefetch"](w)])}async fetchComponent(e){let t=V({route:e,router:this});try{let a=await this.pageLoader.loadPage(e);return t(),a}catch(e){throw t(),e}}_getData(e){let t=!1,a=()=>{t=!0};return this.clc=a,e().then(e=>{if(a===this.clc&&(this.clc=null),t){let e=Error("Loading initial props cancelled");throw e.cancelled=!0,e}return e})}_getFlightData(e){return W({dataHref:e,isServerRender:!0,parseJSON:!1,inflightCache:this.sdc,persistCache:!1,isPrefetch:!1}).then(e=>{let{text:t}=e;return{data:t}})}getInitialProps(e,t){let{Component:a}=this.components["/_app"],r=this._wrapApp(a);return t.AppTree=r,(0,u.loadGetInitialProps)(a,{AppTree:r,Component:e,router:this,ctx:t})}get route(){return this.state.route}get pathname(){return this.state.pathname}get query(){return this.state.query}get asPath(){return this.state.asPath}get locale(){return this.state.locale}get isFallback(){return this.state.isFallback}get isPreview(){return this.state.isPreview}constructor(e,t,r,{initialProps:s,pageLoader:o,App:l,wrapApp:n,Component:h,err:c,subscription:p,isFallback:m,locale:_,locales:P,defaultLocale:v,domainLocales:w,isPreview:y}){this.sdc={},this.sbc={},this.isFirstPopStateEvent=!0,this._key=q(),this.onPopState=e=>{let t;let{isFirstPopStateEvent:a}=this;this.isFirstPopStateEvent=!1;let r=e.state;if(!r){let{pathname:e,query:t}=this;this.changeState("replaceState",(0,g.formatWithValidation)({pathname:(0,S.addBasePath)(e),query:t}),(0,u.getURL)());return}if(r.__NA){window.location.reload();return}if(!r.__N||a&&this.locale===r.options.locale&&r.as===this.asPath)return;let{url:s,as:i,options:o,key:l}=r;this._key=l;let{pathname:n}=(0,f.parseRelativeUrl)(s);(!this.isSsr||i!==(0,S.addBasePath)(this.asPath)||n!==(0,S.addBasePath)(this.pathname))&&(!this._bps||this._bps(r))&&this.change("replaceState",s,i,Object.assign({},o,{shallow:o.shallow&&this._shallow,locale:o.locale||this.defaultLocale,_h:0}),t)};let R=(0,i.removeTrailingSlash)(e);this.components={},"/_error"!==e&&(this.components[R]={Component:h,initial:!0,props:s,err:c,__N_SSG:s&&s.__N_SSG,__N_SSP:s&&s.__N_SSP}),this.components["/_app"]={Component:l,styleSheets:[]};{let{BloomFilter:e}=a(69970),t={numItems:69,errorRate:1e-4,numBits:1323,numHashes:14,bitArray:[1,0,1,1,1,0,1,0,0,1,0,0,1,1,0,0,1,0,0,0,1,1,0,0,0,0,0,0,1,1,0,1,1,1,0,0,0,1,1,0,1,0,1,1,1,1,1,0,1,1,1,0,1,0,1,1,1,1,0,1,0,1,1,0,1,1,1,1,0,0,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,0,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,1,0,1,1,1,0,1,1,1,1,0,0,0,0,1,1,1,0,0,0,1,1,1,1,1,0,0,0,0,0,1,1,1,0,0,0,0,0,1,1,0,1,1,0,0,1,1,1,1,1,1,0,1,1,0,1,0,0,0,0,1,0,0,0,1,1,1,1,1,0,0,1,1,1,1,0,0,0,1,0,0,1,0,1,0,1,0,0,0,1,1,1,1,0,1,1,0,1,0,0,0,0,1,1,0,0,0,1,0,0,0,0,0,1,1,1,0,1,0,0,0,1,1,1,1,0,1,0,0,1,1,1,1,0,0,1,1,0,1,1,0,0,0,1,0,0,0,0,0,0,1,1,0,0,1,0,1,1,0,1,1,0,1,0,0,0,0,1,0,1,0,0,0,0,1,1,1,1,1,0,1,0,0,0,0,0,0,0,1,1,0,1,1,1,1,0,1,1,1,0,1,0,0,1,1,1,0,0,1,1,0,1,0,1,0,1,1,1,0,0,1,1,1,1,1,1,1,0,0,0,1,0,0,0,0,1,1,0,1,1,1,1,1,1,1,1,1,0,0,1,1,0,0,0,0,1,0,1,0,1,1,1,0,0,1,0,0,0,1,0,0,1,1,1,1,0,1,0,1,1,1,0,0,1,0,1,1,1,0,1,1,1,1,0,0,0,0,1,1,0,1,1,0,0,0,1,0,1,1,1,1,1,0,1,1,0,1,1,0,0,0,1,1,1,0,0,1,0,0,0,1,0,1,0,0,1,1,1,0,0,0,0,0,1,1,1,0,0,0,1,1,1,1,1,0,1,0,0,0,1,1,1,1,1,0,0,1,1,0,0,0,0,1,1,1,0,1,1,1,0,1,0,0,1,0,1,0,1,1,0,1,1,0,1,1,1,1,1,1,0,0,1,1,1,1,1,1,1,1,1,0,1,0,0,1,0,1,1,1,0,1,0,1,0,0,0,1,1,0,0,1,1,0,1,0,0,1,0,0,0,1,1,1,1,0,0,0,1,1,0,0,1,1,1,1,0,0,0,0,0,1,1,1,1,1,1,0,0,0,0,0,1,0,1,1,1,1,1,0,0,0,1,0,0,0,0,1,1,1,1,0,1,0,0,0,0,0,1,0,1,1,1,1,1,0,1,0,0,1,1,0,1,1,1,1,1,1,1,1,0,0,1,1,1,1,1,1,1,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,1,0,1,0,0,1,0,1,0,1,1,1,0,1,1,1,1,1,0,1,0,1,1,0,0,1,1,0,1,1,1,0,1,0,0,0,0,0,1,0,0,1,1,1,1,1,0,1,0,0,1,1,1,0,1,1,0,0,1,0,1,0,1,1,1,0,0,1,0,0,0,1,0,0,0,0,1,1,1,1,1,1,1,0,1,0,1,0,0,0,1,1,1,0,1,0,0,1,1,0,0,1,1,0,0,0,0,0,1,0,1,1,1,1,0,1,0,1,0,1,0,1,1,0,1,1,1,0,1,0,1,1,1,1,1,1,1,0,0,1,1,1,0,0,0,1,0,1,1,0,0,0,1,1,1,1,0,1,0,0,0,1,0,0,1,0,1,1,0,1,0,1,0,1,1,1,0,0,1,0,1,0,1,0,1,1,1,1,1,1,1,1,0,1,0,0,0,0,0,1,1,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,1,0,0,1,0,0,1,1,1,1,1,1,0,0,0,0,0,1,1,1,0,0,0,0,0,1,0,0,0,1,1,1,0,0,1,1,0,1,1,1,1,1,1,0,0,0,1,1,1,1,0,0,1,0,0,1,1,0,1,0,0,0,1,0,0,0,1,0,1,1,1,1,0,0,0,1,0,1,0,0,0,0,0,1,1,0,0,0,0,1,0,0,0,1,0,1,1,0,1,0,1,0,1,1,1,1,1,0,1,0,1,1,0,1,0,0,0,0,0,0,1,0,1,1,1,1,1,1,1,1,0,1,0,1,0,1,0,0,1,0,1,1,0,0,0,1,1,0,1,1,1,0,0,1,1,1,0,0,0,0,0,0,1,1,0,1,1,1,1,1,0,0,1,1,1,0,0,1,0,0,1,0,1,0,0,0,1,1,1,0,1,0,0,0,1,0,0,0,0,1,0,1,0,0,1,0,1,1,0,1,0,1,0,1,1,0,1,1,0,1,1,1,0,1,1,1,0,0,1,0,0,1,0,0,1,0,1,0,1,0,1,0,0,0,1,0,0,0,1,1,0,1,0,1,1,0,0,0,1,1,1,0,1,0,0,1,0,0,0,1,1,1,0,0,0,1,0,0,0,1,0,0,1,0,1,0,1,0,0,1,0,0,1,1,1,1,1,1,0,0,1,0,0,0,0,0,0,1,0,1,0,1,1,0,0,1,1,1,1,0,0,0,1,1,1,0,0,0,0,1,1,0,1,1,0,1,0,0,1,0,1,0,0,0,1,1,0,0,0,1,0,1,0,0,1,1,0,1,1,0,0,0,0,0,0,1,1,1,0,1,0,1,1,0,0,1,0,0,0,1,1,1,0,0,1,1,1,0,0,0,0,0,0,0,0,1,1,0,1,0,0,1,0,0,0,1,0,0,1,1,0,0,0,0,1,0,0,1,0,1,0,0,1,1,0,0,1,1,0,0,1,1,1,0,0,0,1,1,0,1,0,1,0,1,0,1,0,0,0,1,0,0,1,1,1,0,1,1]},r={numItems:4,errorRate:1e-4,numBits:77,numHashes:14,bitArray:[1,0,0,0,0,1,1,0,0,1,0,0,0,0,0,1,1,0,1,1,1,0,0,0,1,1,0,0,0,1,1,0,1,1,1,1,0,1,1,0,1,0,0,0,1,1,0,1,1,0,1,1,1,0,1,0,0,1,1,1,0,1,0,0,0,1,0,0,1,0,1,0,0,0,1,0,0]};(null==t?void 0:t.numHashes)&&(this._bfl_s=new e(t.numItems,t.errorRate),this._bfl_s.import(t)),(null==r?void 0:r.numHashes)&&(this._bfl_d=new e(r.numItems,r.errorRate),this._bfl_d.import(r))}this.events=X.events,this.pageLoader=o;let b=(0,d.isDynamicRoute)(e)&&self.__NEXT_DATA__.autoExport;if(this.basePath="",this.sub=p,this.clc=null,this._wrapApp=n,this.isSsr=!0,this.isLocaleDomain=!1,this.isReady=!!(self.__NEXT_DATA__.gssp||self.__NEXT_DATA__.gip||self.__NEXT_DATA__.isExperimentalCompile||self.__NEXT_DATA__.appGip&&!self.__NEXT_DATA__.gsp||!b&&!self.location.search),this.state={route:R,pathname:e,query:t,asPath:b?e:r,isPreview:!!y,locale:void 0,isFallback:m},this._initialMatchesMiddlewarePromise=Promise.resolve(!1),!r.startsWith("//")){let a={locale:_},s=(0,u.getURL)();this._initialMatchesMiddlewarePromise=I({router:this,locale:_,asPath:s}).then(i=>(a._shouldResolveHref=r!==e,this.changeState("replaceState",i?s:(0,g.formatWithValidation)({pathname:(0,S.addBasePath)(e),query:t}),s,a),i))}window.addEventListener("popstate",this.onPopState)}}X.events=(0,p.default)()}}]);