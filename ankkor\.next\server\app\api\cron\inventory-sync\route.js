"use strict";(()=>{var e={};e.id=2440,e.ids=[2440],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},93690:e=>{e.exports=import("graphql-request")},88690:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{originalPathname:()=>v,patchFetch:()=>u,requestAsyncStorage:()=>d,routeModule:()=>l,serverHooks:()=>g,staticGenerationAsyncStorage:()=>p});var n=t(49303),s=t(88716),o=t(60670),c=t(82614),i=e([c]);c=(i.then?(await i)():i)[0];let l=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/cron/inventory-sync/route",pathname:"/api/cron/inventory-sync",filename:"route",bundlePath:"app/api/cron/inventory-sync/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\cron\\inventory-sync\\route.ts",nextConfigOutput:"",userland:c}),{requestAsyncStorage:d,staticGenerationAsyncStorage:p,serverHooks:g}=l,v="/api/cron/inventory-sync/route";function u(){return(0,o.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:p})}a()}catch(e){a(e)}})},82614:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{GET:()=>i,dynamic:()=>u});var n=t(87070),s=t(63706),o=t(19910),c=e([o]);o=(c.then?(await c)():c)[0];let u="force-dynamic";async function i(e){try{let r=e.headers.get("authorization"),t=process.env.CRON_SECRET_TOKEN;if(!r||r!==`Bearer ${t}`)return n.NextResponse.json({error:"Unauthorized"},{status:401});let a=await (0,o.Dg)();console.log(`Fetched ${a?.length||0} products`);let c=await (0,s.Jm)();return n.NextResponse.json({success:!0,productCount:a?.length||0,revalidated:c.success,timestamp:new Date().toISOString()})}catch(e){return console.error("Error in inventory-sync:",e),n.NextResponse.json({error:"Inventory sync failed",message:e instanceof Error?e.message:"Unknown error",timestamp:new Date().toISOString()},{status:500})}}a()}catch(e){a(e)}})},90152:(e,r,t)=>{t.d(r,{C2:()=>c,S$:()=>u,ck:()=>i});var a=t(24330);t(60166);var n=t(57708),s=t(71615),o=t(58585);async function c(e){try{return(0,n.revalidatePath)(`/product/${e}`),(0,n.revalidateTag)(`product-${e}`),(0,n.revalidatePath)("/collection"),(0,n.revalidateTag)("products"),{success:!0,message:`Successfully revalidated product: ${e}`}}catch(r){return console.error(`Error revalidating product ${e}:`,r),{success:!1,message:r instanceof Error?r.message:"Unknown error during revalidation"}}}async function i(){try{return(0,n.revalidatePath)("/product"),(0,n.revalidatePath)("/collection"),(0,n.revalidateTag)("products"),(0,n.revalidateTag)("inventory"),{success:!0,message:"Successfully revalidated inventory"}}catch(e){return console.error("Error revalidating inventory:",e),{success:!1,message:e instanceof Error?e.message:"Unknown error during inventory revalidation"}}}async function u(){try{return(0,n.revalidatePath)("/","layout"),(0,n.revalidateTag)("global"),{success:!0,message:"Successfully revalidated all pages"}}catch(e){return console.error("Error revalidating all pages:",e),{success:!1,message:e instanceof Error?e.message:"Unknown error during site-wide revalidation"}}}async function l(e){try{return(0,n.revalidatePath)(`/collection/${e}`),(0,n.revalidateTag)(`collection-${e}`),(0,n.revalidatePath)("/collection"),{success:!0,message:`Successfully revalidated collection: ${e}`}}catch(r){return console.error(`Error revalidating collection ${e}:`,r),{success:!1,message:r instanceof Error?r.message:"Unknown error during collection revalidation"}}}async function d(e,r,t=2592e3){try{return(0,s.cookies)().set(e,r,{maxAge:t,path:"/",sameSite:"lax",secure:!0}),{success:!0}}catch(r){return console.error(`Error setting preference cookie ${e}:`,r),{success:!1}}}async function p(e){try{return(0,s.cookies)().delete(e),{success:!0}}catch(r){return console.error(`Error deleting preference cookie ${e}:`,r),{success:!1}}}async function g(e){(0,o.redirect)(e)}(0,t(40618).h)([c,i,u,l,d,p,g]),(0,a.j)("a50717f48f6a2815a952ed293c1ffcd237e84398",c),(0,a.j)("4aa66c79eaba83e398a49c79f548be0f77e35a74",i),(0,a.j)("7acd90ecc7717585a4e59fd620100679aac11a1c",u),(0,a.j)("d64dc9d742eb29b5e8020c257b5ce339a41cb5ec",l),(0,a.j)("67efe3eacae6c151748c818acc5c136660a35769",d),(0,a.j)("db63bd4ffc5b15cee559935b44ade40fe5ef3b60",p),(0,a.j)("dfe708f68eaa721bbf91e3563a432bf0e6758d82",g)},63706:(e,r,t)=>{t.d(r,{Jm:()=>n});var a=t(90152);let n=async()=>{try{return await (0,a.ck)(),{success:!0,errors:[]}}catch(r){let e=r instanceof Error?r.message:"Unknown error during inventory revalidation";return console.error("Error revalidating inventory:",e),{success:!1,errors:[e]}}}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8948,5972,4766,4868,7708,2727,9910],()=>t(88690));module.exports=a})();