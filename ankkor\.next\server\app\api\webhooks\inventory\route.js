"use strict";(()=>{var e={};e.id=3673,e.ids=[3673],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},38793:(e,o,t)=>{t.r(o),t.d(o,{originalPathname:()=>v,patchFetch:()=>E,requestAsyncStorage:()=>_,routeModule:()=>S,serverHooks:()=>f,staticGenerationAsyncStorage:()=>w});var r={};t.r(r),t.d(r,{GET:()=>p,POST:()=>g});var n=t(49303),s=t(88716),a=t(60670),c=t(87070),i=t(94868),l=t(92861);let d=process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN?new i.s({url:process.env.UPSTASH_REDIS_REST_URL,token:process.env.UPSTASH_REDIS_REST_TOKEN}):null,u={PRODUCTS:86400};async function p(){return c.NextResponse.json({message:"WooCommerce Inventory Webhook Endpoint",status:"active",timestamp:new Date().toISOString()})}async function g(e){console.log("=== WooCommerce Webhook Received ===");try{let o,t;let r=Object.fromEntries(e.headers.entries());console.log("Headers:",r);try{o=await e.text(),console.log("Body received, length:",o.length),console.log("Body preview:",o.substring(0,200))}catch(e){return console.error("Error reading request body:",e),c.NextResponse.json({error:"Failed to read request body",details:e instanceof Error?e.message:"Unknown error"},{status:400})}if(!o||0===o.length)return console.error("Empty request body received"),c.NextResponse.json({error:"Empty request body"},{status:400});let n=e.headers.get("content-type")||"";if(console.log("Content-Type:",n),n.includes("application/x-www-form-urlencoded")){let e=new URLSearchParams(o);if(t=Object.fromEntries(e.entries()),console.log("Parsed form data:",t),t.webhook_id)return console.log("Received webhook test ping, webhook_id:",t.webhook_id),c.NextResponse.json({success:!0,message:"Webhook test ping received successfully",webhook_id:t.webhook_id,timestamp:new Date().toISOString()})}else try{t=JSON.parse(o),console.log("JSON parsed successfully")}catch(e){return console.error("JSON parse error:",e),c.NextResponse.json({error:"Invalid JSON in request body",details:e instanceof Error?e.message:"Unknown error"},{status:400})}console.log("Webhook data received:",{id:t?.id,name:t?.name,slug:t?.slug,stock_status:t?.stock_status,stock_quantity:t?.stock_quantity}),console.log("Skipping signature verification for debugging");let s=e.headers.get("x-wc-webhook-topic")||"product.updated";console.log("Webhook topic:",s);try{switch(s){case"product.created":await k(t);break;case"product.updated":await y(t);break;case"product.deleted":await m(t);break;default:console.log("Handling as product update (default)"),await y(t)}console.log("Webhook handled successfully")}catch(e){console.error("Error in handleProductUpdate:",e)}return console.log("=== Webhook Processing Complete ==="),c.NextResponse.json({success:!0,message:"Webhook processed successfully",productId:t?.id||"unknown",timestamp:new Date().toISOString()})}catch(e){return console.error("=== Critical Webhook Error ==="),console.error("Error type:",typeof e),console.error("Error message:",e instanceof Error?e.message:String(e)),console.error("Error stack:",e instanceof Error?e.stack:"No stack trace"),c.NextResponse.json({success:!1,error:"Webhook processing failed",details:e instanceof Error?e.message:"Unknown error",timestamp:new Date().toISOString()},{status:200})}}async function k(e){try{if(!e||!e.id){console.error("Invalid product data received:",e);return}let o=e.id.toString(),t=e.slug;if(console.log(`Processing new product creation: ${t} (ID: ${o})`),await l.ho(o,t),d)try{let o=`product:${t}`,r={id:e.id,databaseId:e.id,name:e.name,slug:e.slug,stockStatus:e.stock_status,stockQuantity:e.stock_quantity,availableForSale:"instock"===e.stock_status,_lastInventoryUpdate:new Date().toISOString(),_stockUpdateSource:"webhook_created"};await d.set(o,r,{ex:u.PRODUCTS}),console.log(`Cached new product ${t}`)}catch(e){console.warn("Cache creation failed:",e)}await h(o,{type:"product_created",stockStatus:e.stock_status,stockQuantity:e.stock_quantity,availableForSale:"instock"===e.stock_status})}catch(e){console.error("Error handling product creation:",e)}}async function m(e){try{if(!e||!e.id){console.error("Invalid product data received:",e);return}let o=e.id.toString(),t=e.slug;if(console.log(`Processing product deletion: ${t} (ID: ${o})`),d)try{let e=`product:${t}`;await d.del(e),console.log(`Removed product ${t} from cache`)}catch(e){console.warn("Cache deletion failed:",e)}await h(o,{type:"product_deleted",stockStatus:"deleted",availableForSale:!1})}catch(e){console.error("Error handling product deletion:",e)}}async function y(e){console.log("=== handleProductUpdate called ===");try{if(!e){console.log("No product data provided");return}if(!e.id){console.log("Product missing ID field");return}let o=e.id.toString(),t=e.slug||`product-${o}`;console.log(`Processing product: ${t} (ID: ${o})`);try{await l.ho(o,t),console.log("Inventory mapping updated successfully")}catch(e){console.error("Inventory mapping failed:",e)}if(d)try{let r=`product:${t}`,n=e.stock_status||"unknown",s=e.stock_quantity||0,a={id:o,slug:t,stockStatus:n,stockQuantity:s,availableForSale:"instock"===n,_lastInventoryUpdate:new Date().toISOString(),_stockUpdateSource:"webhook"};await d.set(r,a,{ex:u.PRODUCTS}),console.log(`Cache updated: ${t} -> ${n} (${s})`)}catch(e){console.error("Cache update failed:",e)}else console.log("Redis not available, skipping cache update");try{await h(o,{stockStatus:e.stock_status,stockQuantity:e.stock_quantity,availableForSale:"instock"===e.stock_status}),console.log("Stock update broadcast completed")}catch(e){console.error("Broadcast failed:",e)}console.log("=== handleProductUpdate completed ===")}catch(e){console.error("=== handleProductUpdate error ==="),console.error("Error details:",e)}}async function h(e,o){if(console.log("Broadcasting stock update:",{productId:e,stockData:o}),d)try{await d.set(`stock_update:${e}`,{...o,timestamp:Date.now()},{ex:60})}catch(e){console.warn("Failed to broadcast stock update to Redis:",e)}}let S=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/webhooks/inventory/route",pathname:"/api/webhooks/inventory",filename:"route",bundlePath:"app/api/webhooks/inventory/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\webhooks\\inventory\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:_,staticGenerationAsyncStorage:w,serverHooks:f}=S,v="/api/webhooks/inventory/route";function E(){return(0,a.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:w})}},92861:(e,o,t)=>{t.d(o,{Ls:()=>d,ho:()=>i,p_:()=>l,wm:()=>u});var r=t(94868);let n="woo:inventory:mapping:",s=new r.s({url:process.env.UPSTASH_REDIS_REST_URL||process.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""}),a={};function c(){return!!(process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_URL&&process.env.NEXT_PUBLIC_KV_REST_API_TOKEN)}async function i(e,o){try{return c()?(await s.set(`${n}${e}`,o),console.log(`Added WooCommerce mapping to Redis: ${e} -> ${o}`)):(a[e]=o,console.log(`Added WooCommerce mapping to memory: ${e} -> ${o}`)),!0}catch(t){console.error("Error adding WooCommerce inventory mapping:",t);try{return a[e]=o,console.log(`Added WooCommerce mapping to memory fallback: ${e} -> ${o}`),!0}catch(e){return console.error("Error adding to memory fallback:",e),!1}}}async function l(e){try{if(c()){let o=s.pipeline();for(let{productId:t,productSlug:r}of e)o.set(`${n}${t}`,r);await o.exec(),console.log(`Updated ${e.length} WooCommerce inventory mappings in Redis`)}else{for(let{productId:o,productSlug:t}of e)a[o]=t;console.log(`Updated ${e.length} WooCommerce inventory mappings in memory`)}return!0}catch(e){return console.error("Error batch updating WooCommerce inventory mappings:",e),!1}}async function d(){try{if(c()){let e=await s.keys(`${n}*`);if(e.length>0){let o={},t=await s.mget(...e);return e.forEach((e,r)=>{let s=e.replace(n,""),a=t[r];o[s]={wooId:s,inventory:0,sku:"",title:a,lastUpdated:new Date().toISOString()}}),o}}return{}}catch(e){return console.error("Error getting inventory mapping:",e),{}}}async function u(e){try{if(c()){let o=await s.keys(`${n}*`);o.length>0&&await s.del(...o);let t=s.pipeline();for(let[o,r]of Object.entries(e))t.set(`${n}${o}`,r.title||o);return await t.exec(),!0}return!1}catch(e){return console.error("Error updating inventory mapping:",e),!1}}}};var o=require("../../../../webpack-runtime.js");o.C(e);var t=e=>o(o.s=e),r=o.X(0,[8948,5972,4766,4868],()=>t(38793));module.exports=r})();