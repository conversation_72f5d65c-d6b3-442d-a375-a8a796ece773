(()=>{var e={};e.id=1599,e.ids=[1599],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},67969:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{GlobalError:()=>i.Z,__next_app__:()=>m,originalPathname:()=>f,pages:()=>p,routeModule:()=>g,tree:()=>u});var o=r(8179);r(11360),r(7629),r(11930),r(12523);var s=r(23191),a=r(88716),i=r(43315),l=r(95231),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);r.d(t,c);var d=e([o]);o=(d.then?(await d)():d)[0];let u=["",{children:["product",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8179)),"E:\\ankkorwoo\\ankkor\\src\\app\\product\\[slug]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,11360)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,7629)),"E:\\ankkorwoo\\ankkor\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,11930)),"E:\\ankkorwoo\\ankkor\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],p=["E:\\ankkorwoo\\ankkor\\src\\app\\product\\[slug]\\page.tsx"],f="/product/[slug]/page",m={require:r,loadChunk:()=>Promise.resolve()},g=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/product/[slug]/page",pathname:"/product/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}});n()}catch(e){n(e)}})},72996:(e,t,r)=>{Promise.resolve().then(r.bind(r,29410))},34565:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(76557).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},46226:(e,t,r)=>{"use strict";r.d(t,{default:()=>o.a});var n=r(69029),o=r.n(n)},69029:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return i}});let n=r(91174),o=r(23078),s=r(92481),a=n._(r(86820));function i(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=s.Image},29410:(e,t,r)=>{"use strict";r.d(t,{default:()=>m});var n=r(10326),o=r(17577),s=r(46226),a=r(92148),i=r(86806),l=r(72248),c=r(77321),d=r(76557);let u=(0,d.Z)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]),p=(0,d.Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var f=r(34565);let m=({product:e})=>{let[t,r]=(0,o.useState)(0),[d,m]=(0,o.useState)(1),[g,x]=(0,o.useState)(null),[h,v]=(0,o.useState)({}),[y,b]=(0,o.useState)(!1),j=(0,i.rY)(),{openCart:k}=(0,c.j)(),{id:S,databaseId:w,name:N,description:_,shortDescription:P,price:R,regularPrice:E,onSale:O,stockStatus:C,image:M,galleryImages:T,attributes:A,type:F,variations:U}=e,{stockData:q,isConnected:I}=function(e,t=!0){let[r,n]=(0,o.useState)({}),{isConnected:s,error:a}=function({productIds:e=[],onStockUpdate:t,enabled:r=!0}={}){let[n,s]=(0,o.useState)(!1),[a,i]=(0,o.useState)(null),[l,c]=(0,o.useState)(null),d=(0,o.useRef)(null),u=(0,o.useRef)(null),p=(0,o.useMemo)(()=>e,[e.join(",")]),f=(0,o.useCallback)(e=>{t?.(e)},[t]),m=(0,o.useCallback)(()=>{if(d.current&&(d.current.close(),d.current=null),u.current&&(clearTimeout(u.current),u.current=null),!r||0===p.length)return console.log("Stock updates disabled or no products specified"),null;let e=new URLSearchParams({products:p.join(",")});console.log("Creating new stock updates connection for products:",p);let t=new EventSource(`/api/stock-updates?${e}`);return d.current=t,t.onopen=()=>{console.log("Stock updates stream connected for products:",p),s(!0),c(null)},t.onmessage=e=>{try{let t=JSON.parse(e.data);i(t),"stock_update"===t.type&&(console.log("Stock update received:",t),f(t))}catch(e){console.error("Error parsing stock update:",e)}},t.onerror=e=>{console.error("Stock updates stream error:",e),s(!1),c("Connection to stock updates failed"),d.current===t&&(u.current=setTimeout(()=>{d.current===t&&t.readyState===EventSource.CLOSED&&(console.log("Attempting to reconnect stock updates..."),m())},5e3))},t},[p,f,r]);return{isConnected:n,lastUpdate:a,error:l,reconnect:m}}({productIds:(0,o.useMemo)(()=>e?[e]:[],[e]),onStockUpdate:(0,o.useCallback)(t=>{t.productId===e&&n(e=>({...e,stockStatus:t.stockStatus,stockQuantity:t.stockQuantity,availableForSale:t.availableForSale,lastUpdated:t.timestamp}))},[e]),enabled:t&&!!e});return{stockData:r,isConnected:s,error:a}}(w?.toString()||"",!0),L=q.stockStatus||C,z=q.stockQuantity,$="VARIABLE"===F,D=[M?.sourceUrl?{sourceUrl:M.sourceUrl,altText:M.altText||N}:null,...T?.nodes||[]].filter(Boolean),Z=(e,t)=>{if(v(r=>({...r,[e]:t})),$&&U?.nodes){let r={...h,[e]:t};if(A?.nodes?.every(e=>r[e.name])){let e=U.nodes.find(e=>e.attributes.nodes.every(e=>{let t=r[e.name];return e.value===t}));e?x(e):x(null)}}},V=async()=>{b(!0);try{let e={productId:w.toString(),quantity:d,name:N,price:g?.price||R,image:{url:D[0]?.sourceUrl||"",altText:D[0]?.altText||N}};await j.addToCart(e),k()}catch(e){console.error("Error adding product to cart:",e)}finally{b(!1)}},G="IN_STOCK"!==(L||C)&&"instock"!==(L||C),W=!$||$&&g;return n.jsx("div",{className:"container mx-auto px-4 py-12",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,n.jsxs)("div",{className:"space-y-6",children:[n.jsx("div",{className:"relative aspect-square bg-[#f4f3f0] overflow-hidden",children:D[t]?.sourceUrl&&n.jsx(s.default,{src:D[t].sourceUrl,alt:D[t].altText||N,fill:!0,sizes:"(max-width: 768px) 100vw, 50vw",priority:!0,className:"object-cover"})}),D.length>1&&n.jsx("div",{className:"grid grid-cols-5 gap-2",children:D.map((e,o)=>n.jsx("button",{onClick:()=>r(o),className:`relative aspect-square bg-[#f4f3f0] ${t===o?"ring-2 ring-[#2c2c27]":""}`,children:n.jsx(s.default,{src:e.sourceUrl,alt:e.altText||`${N} - Image ${o+1}`,fill:!0,sizes:"(max-width: 768px) 20vw, 10vw",className:"object-cover"})},o))})]}),(0,n.jsxs)("div",{className:"space-y-6",children:[n.jsx("h1",{className:"text-3xl font-serif text-[#2c2c27]",children:N}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[n.jsx("span",{className:"text-xl font-medium text-[#2c2c27]",children:(g?.price||R).toString().includes("₹")||(g?.price||R).toString().includes("$")||(g?.price||R).toString().includes("€")||(g?.price||R).toString().includes("\xa3")?g?.price||R:`₹${g?.price||R}`}),O&&E&&n.jsx("span",{className:"text-sm line-through text-[#8a8778]",children:E.toString().includes("₹")||E.toString().includes("$")||E.toString().includes("€")||E.toString().includes("\xa3")?E:`₹${E}`})]}),P&&n.jsx("div",{className:"prose prose-sm text-[#5c5c52]",dangerouslySetInnerHTML:{__html:P}}),$&&A?.nodes&&n.jsx("div",{className:"space-y-4",children:A.nodes.map(e=>(0,n.jsxs)("div",{className:"space-y-2",children:[n.jsx("h3",{className:"font-medium text-[#2c2c27]",children:e.name}),n.jsx("div",{className:"flex flex-wrap gap-2",children:e.options.map(t=>n.jsx("button",{onClick:()=>Z(e.name,t),className:`px-4 py-2 border ${h[e.name]===t?"border-[#2c2c27] bg-[#2c2c27] text-white":"border-gray-300 hover:border-[#8a8778]"}`,children:t},t))})]},e.name))}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[n.jsx("span",{className:"text-[#5c5c52]",children:"Quantity:"}),(0,n.jsxs)("div",{className:"flex items-center border border-gray-300",children:[n.jsx("button",{onClick:()=>m(e=>e>1?e-1:1),disabled:d<=1,className:"px-3 py-2 hover:bg-gray-100","aria-label":"Decrease quantity",children:n.jsx(u,{className:"h-4 w-4"})}),n.jsx("span",{className:"px-4 py-2 border-x border-gray-300",children:d}),n.jsx("button",{onClick:()=>m(e=>e+1),className:"px-3 py-2 hover:bg-gray-100","aria-label":"Increase quantity",children:n.jsx(p,{className:"h-4 w-4"})})]})]}),(0,n.jsxs)("div",{className:"text-sm",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[n.jsx("span",{className:"font-medium",children:"Availability: "}),n.jsx("span",{className:G?"text-red-600":"text-green-600",children:G?"Out of Stock":"In Stock"})]}),null!=z&&n.jsx("div",{className:"text-xs text-gray-600 mt-1",children:z>0?(0,n.jsxs)("span",{children:[z," items available"]}):n.jsx("span",{className:"text-red-600",children:"No items in stock"})}),q.lastUpdated&&(0,n.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Last updated: ",new Date(q.lastUpdated).toLocaleTimeString()]})]}),(0,n.jsxs)(a.E.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},children:[(0,n.jsxs)(l.z,{onClick:V,disabled:G||y||!W,className:"w-full py-6 bg-[#2c2c27] text-white hover:bg-[#3c3c37] flex items-center justify-center gap-2",children:[n.jsx(f.Z,{className:"h-5 w-5"}),y?"Adding...":"Add to Cart"]}),$&&!W&&!G&&n.jsx("p",{className:"mt-2 text-sm text-red-600",children:"Please select all options to add this product to your cart"})]}),_&&(0,n.jsxs)("div",{className:"mt-12 border-t border-gray-200 pt-8",children:[n.jsx("h2",{className:"text-xl font-serif mb-4 text-[#2c2c27]",children:"Description"}),n.jsx("div",{className:"prose prose-sm text-[#5c5c52]",dangerouslySetInnerHTML:{__html:_}})]})]})]})})}},72248:(e,t,r)=>{"use strict";let n,o;r.d(t,{z:()=>p});var s=r(10326);r(17577);var a=r(34214),i=r(41135);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,c=i.W;var d=r(51223);let u=(n="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",o={variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}},e=>{var t;if((null==o?void 0:o.variants)==null)return c(n,null==e?void 0:e.class,null==e?void 0:e.className);let{variants:r,defaultVariants:s}=o,a=Object.keys(r).map(t=>{let n=null==e?void 0:e[t],o=null==s?void 0:s[t];if(null===n)return null;let a=l(n)||l(o);return r[t][a]}),i=e&&Object.entries(e).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return c(n,a,null==o?void 0:null===(t=o.compoundVariants)||void 0===t?void 0:t.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...i}[t]):({...s,...i})[t]===r})?[...e,r,n]:e},[]),null==e?void 0:e.class,null==e?void 0:e.className)});function p({className:e,variant:t,size:r,asChild:n=!1,...o}){let i=n?a.g7:"button";return s.jsx(i,{"data-slot":"button",className:(0,d.cn)(u({variant:t,size:r,className:e})),...o})}},51223:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var n=r(41135),o=r(31009);function s(...e){return(0,o.m6)((0,n.W)(e))}},58585:(e,t,r)=>{"use strict";var n=r(61085);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}}),r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}})},61085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return a},RedirectType:function(){return n.RedirectType},notFound:function(){return o.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect}});let n=r(83953),o=r(16399);class s extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class a extends URLSearchParams{append(){throw new s}delete(){throw new s}set(){throw new s}sort(){throw new s}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return o},notFound:function(){return n}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function o(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return n},getRedirectError:function(){return l},getRedirectStatusCodeFromError:function(){return m},getRedirectTypeFromError:function(){return f},getURLFromRedirectError:function(){return p},isRedirectError:function(){return u},permanentRedirect:function(){return d},redirect:function(){return c}});let o=r(54580),s=r(72934),a=r(8586),i="NEXT_REDIRECT";function l(e,t,r){void 0===r&&(r=a.RedirectStatusCode.TemporaryRedirect);let n=Error(i);n.digest=i+";"+t+";"+e+";"+r+";";let s=o.requestAsyncStorage.getStore();return s&&(n.mutableCookies=s.mutableCookies),n}function c(e,t){void 0===t&&(t="replace");let r=s.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?a.RedirectStatusCode.SeeOther:a.RedirectStatusCode.TemporaryRedirect)}function d(e,t){void 0===t&&(t="replace");let r=s.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?a.RedirectStatusCode.SeeOther:a.RedirectStatusCode.PermanentRedirect)}function u(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,n,o]=e.digest.split(";",4),s=Number(o);return t===i&&("replace"===r||"push"===r)&&"string"==typeof n&&!isNaN(s)&&s in a.RedirectStatusCode}function p(e){return u(e)?e.digest.split(";",3)[2]:null}function f(e){if(!u(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function m(e){if(!u(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8179:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{default:()=>d,generateMetadata:()=>c});var o=r(19510),s=r(58585),a=r(19910),i=r(80151),l=e([a]);async function c({params:e}){let{slug:t}=e;try{let e=await (0,a.gF)(t);if(!e)return{title:"Product Not Found | Ankkor",description:"The requested product could not be found."};return{title:`${e.name} | Ankkor`,description:e.shortDescription||e.description||"Luxury menswear from Ankkor.",openGraph:{images:e.image?[{url:e.image.sourceUrl,alt:e.name}]:[]}}}catch(e){return console.error("Error generating product metadata:",e),{title:"Product | Ankkor",description:"Luxury menswear from Ankkor."}}}async function d({params:e}){let{slug:t}=e;try{let e=await (0,a.gF)(t);return e||(0,s.notFound)(),o.jsx(i.Z,{product:e})}catch(e){console.error("Error fetching product:",e),(0,s.notFound)()}}a=(l.then?(await l)():l)[0],n()}catch(e){n(e)}})},80151:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\product\ProductDetail.tsx#default`)},48051:(e,t,r)=>{"use strict";r.d(t,{F:()=>s,e:()=>a});var n=r(17577);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function a(...e){return n.useCallback(s(...e),e)}},34214:(e,t,r)=>{"use strict";r.d(t,{g7:()=>a});var n=r(17577),o=r(48051),s=r(10326),a=n.forwardRef((e,t)=>{let{children:r,...o}=e,a=n.Children.toArray(r),l=a.find(c);if(l){let e=l.props.children,r=a.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(i,{...o,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,s.jsx)(i,{...o,ref:t,children:r})});a.displayName="Slot";var i=n.forwardRef((e,t)=>{let{children:r,...s}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),a=function(e,t){let r={...t};for(let n in t){let o=e[n],s=t[n];/^on[A-Z]/.test(n)?o&&s?r[n]=(...e)=>{s(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...s}:"className"===n&&(r[n]=[o,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==n.Fragment&&(a.ref=t?(0,o.F)(t,e):e),n.cloneElement(r,a)}return n.Children.count(r)>1?n.Children.only(null):null});i.displayName="SlotClone";var l=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});function c(e){return n.isValidElement(e)&&e.type===l}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8948,1057,4766,4868,2481,325,5436,9910],()=>r(67969));module.exports=n})();