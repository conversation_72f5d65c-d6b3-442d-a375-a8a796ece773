"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5064],{25064:function(e,t,s){s.r(t),s.d(t,{default:function(){return P}});var l=s(57437),a=s(2265),r=s(27648),i=s(33145),c=s(73247),o=s(92369),n=s(47692),d=s(88997),h=s(42449),x=s(58293),u=s(32489),f=s(72792),m=s(93448);function p(e){let{...t}=e;return(0,l.jsx)(f.fC,{"data-slot":"sheet",...t})}function v(e){let{...t}=e;return(0,l.jsx)(f.xz,{"data-slot":"sheet-trigger",...t})}function j(e){let{...t}=e;return(0,l.jsx)(f.x8,{"data-slot":"sheet-close",...t})}function b(e){let{...t}=e;return(0,l.jsx)(f.h_,{"data-slot":"sheet-portal",...t})}function g(e){let{className:t,...s}=e;return(0,l.jsx)(f.aV,{"data-slot":"sheet-overlay",className:(0,m.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",t),...s})}function N(e){let{className:t,children:s,side:a="right",...r}=e;return(0,l.jsxs)(b,{children:[(0,l.jsx)(g,{}),(0,l.jsxs)(f.VY,{"data-slot":"sheet-content",className:(0,m.cn)("bg-[#f8f8f5] data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===a&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===a&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===a&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===a&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...r,children:[s,(0,l.jsxs)(f.x8,{className:"ring-offset-[#f8f8f5] focus:ring-[#8a8778] data-[state=open]:bg-[#e5e2d9] absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,l.jsx)(u.Z,{className:"size-4"}),(0,l.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}var w=s(92371),y=s(18686),k=s(3371),C=s(76858),S=s(99376),E=s(29658),Z=s(82372),A=e=>{let{isOpen:t,onClose:s}=e,[r,o]=(0,a.useState)(""),[n,d]=(0,a.useState)(!1),[h,x]=(0,a.useState)(!0),[f,m]=(0,a.useState)([]),[p,v]=(0,a.useState)([]),[j,b]=(0,a.useState)(!1),g=(0,a.useRef)(null),N=(0,a.useRef)(null),w=(0,S.useRouter)(),y=function(e,t){let[s,l]=(0,a.useState)(e);return(0,a.useEffect)(()=>{let t=setTimeout(()=>{l(e)},150);return()=>{clearTimeout(t)}},[e,150]),s}(r,0);(0,a.useEffect)(()=>{let e=async()=>{try{x(!0);let e=(await (0,Z.getAllProducts)(100)).map(e=>{var t,s,l,a,r,i,c,o,n,d,h,x,u,f;let m=(null===(l=e.variants)||void 0===l?void 0:null===(s=l.edges)||void 0===s?void 0:null===(t=s[0])||void 0===t?void 0:t.node)||null,p=(null===(c=e.images)||void 0===c?void 0:null===(i=c.edges)||void 0===i?void 0:null===(r=i[0])||void 0===r?void 0:null===(a=r.node)||void 0===a?void 0:a.url)||"",v=(null===(h=e.collections)||void 0===h?void 0:null===(d=h.edges)||void 0===d?void 0:null===(n=d[0])||void 0===n?void 0:null===(o=n.node)||void 0===o?void 0:o.handle)||"clothing";return{id:e.id||"",title:e.title||"Untitled Product",handle:e.handle||"",description:e.description||"",image:p,price:(null==m?void 0:null===(x=m.price)||void 0===x?void 0:x.amount)||(null===(f=e.priceRange)||void 0===f?void 0:null===(u=f.minVariantPrice)||void 0===u?void 0:u.amount)||"0.00",tags:Array.isArray(e.tags)?e.tags:[],category:v}});m(e)}catch(e){console.error("Error loading products for search:",e)}finally{x(!1)}};t&&e()},[t]),(0,a.useEffect)(()=>{t&&g.current&&g.current.focus()},[t]),(0,a.useEffect)(()=>{if(!y.trim()||y.length<2){v([]),b(!1);return}d(!0);let e=y.toLowerCase().split(" ").filter(e=>e.length>0),t=f.filter(t=>!!t&&e.every(e=>{var s,l,a,r;let i=null===(s=t.title)||void 0===s?void 0:s.toLowerCase().includes(e),c=null===(l=t.description)||void 0===l?void 0:l.toLowerCase().includes(e),o=null===(a=t.tags)||void 0===a?void 0:a.some(t=>t.toLowerCase().includes(e)),n=null===(r=t.category)||void 0===r?void 0:r.toLowerCase().includes(e);return i||c||o||n})).slice(0,5);v(t),b(t.length>0),d(!1)},[y,f]),(0,a.useEffect)(()=>{let e=e=>{N.current&&g.current&&!N.current.contains(e.target)&&!g.current.contains(e.target)&&b(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);let k=e=>{e.preventDefault(),r.trim()&&(d(!0),b(!1),w.push("/search?q=".concat(encodeURIComponent(r.trim()))),setTimeout(()=>{d(!1),s()},300))},A=e=>{b(!1),w.push("/product/".concat(e)),s()};return t?(0,l.jsx)("div",{className:"fixed inset-0 z-[102] flex items-start justify-center bg-[#2c2c27]/90 pt-24 px-4",children:(0,l.jsxs)("div",{className:"w-full max-w-2xl bg-[#f8f8f5] rounded-lg shadow-xl overflow-hidden",children:[(0,l.jsxs)("div",{className:"p-4 border-b border-[#e5e2d9]",children:[(0,l.jsxs)("form",{onSubmit:k,className:"relative",children:[(0,l.jsx)("input",{ref:g,type:"text",value:r,onChange:e=>o(e.target.value),onKeyDown:e=>{"Escape"===e.key&&s()},placeholder:"Search for products...",className:"w-full pl-10 pr-10 py-3 border-none bg-transparent text-[#2c2c27] placeholder-[#8a8778] focus:outline-none focus:ring-0"}),(0,l.jsx)(c.Z,{className:"absolute left-0 top-1/2 -translate-y-1/2 h-5 w-5 text-[#8a8778]"}),(0,l.jsx)("button",{type:"button",onClick:s,className:"absolute right-0 top-1/2 -translate-y-1/2 h-8 w-8 flex items-center justify-center text-[#8a8778] hover:text-[#2c2c27] transition-colors",children:(0,l.jsx)(u.Z,{className:"h-5 w-5"})})]}),j&&(0,l.jsxs)("div",{ref:N,className:"absolute z-10 mt-1 w-full max-w-2xl bg-[#f8f8f5] border border-[#e5e2d9] rounded-lg shadow-lg overflow-hidden",children:[(0,l.jsx)("div",{className:"max-h-96 overflow-y-auto",children:p.map(e=>(0,l.jsxs)("div",{onClick:()=>A(e.handle),className:"flex items-center p-3 hover:bg-[#f4f3f0] cursor-pointer transition-colors border-b border-[#e5e2d9] last:border-0",children:[(0,l.jsx)("div",{className:"flex-shrink-0 w-16 h-16 bg-[#f4f3f0] overflow-hidden rounded",children:e.image&&(0,l.jsx)(i.default,{src:e.image,alt:e.title,width:64,height:64,className:"w-full h-full object-cover"})}),(0,l.jsxs)("div",{className:"ml-4 flex-1",children:[(0,l.jsx)("h4",{className:"text-[#2c2c27] font-medium line-clamp-1",children:e.title}),(0,l.jsxs)("p",{className:"text-[#8a8778] text-sm mt-1",children:["₹",parseFloat(e.price).toFixed(2)]})]}),(0,l.jsx)(C.Z,{className:"h-4 w-4 text-[#8a8778] ml-2"})]},e.id))}),(0,l.jsx)("div",{className:"p-3 border-t border-[#e5e2d9] bg-[#f4f3f0]",children:(0,l.jsxs)("button",{onClick:k,className:"w-full text-[#2c2c27] text-sm font-medium py-2 flex items-center justify-center",children:["View all results",(0,l.jsx)(C.Z,{className:"h-4 w-4 ml-2"})]})})]})]}),(0,l.jsx)("div",{className:"p-4 text-[#5c5c52] text-sm",children:h?(0,l.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,l.jsx)(E.Z,{size:"md",color:"#8a8778"})}):n&&!j?(0,l.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,l.jsx)(E.Z,{size:"md",color:"#8a8778"})}):(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("p",{className:"font-medium",children:"Popular Searches:"}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:["Shirt","Pant","Polo"].map(e=>(0,l.jsx)("button",{onClick:()=>{o(e),g.current&&g.current.focus()},className:"px-3 py-1 bg-[#f4f3f0] rounded-full text-[#5c5c52] hover:bg-[#e5e2d9] transition-colors",children:e},e))})]})})]})}):null},z=()=>{let{toggleCart:e,itemCount:t}=(0,y.j)(),{items:s}=(0,w.useWishlistStore)(),f=s.length,[m,b]=a.useState(!1),[g,C]=(0,a.useState)(!1),{customer:S,isAuthenticated:E,logout:Z}=(0,k.O)();return a.useEffect(()=>{let e=()=>{window.scrollY>10?b(!0):b(!1)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),a.useEffect(()=>{let e=e=>{"Escape"===e.key&&g&&C(!1)};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[g]),a.useEffect(()=>{console.log("Navbar: Authentication state",{isAuthenticated:E,customer:S?"".concat(S.firstName," ").concat(S.lastName):"not logged in"})},[E,S]),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("nav",{className:"fixed top-0 left-0 right-0 z-[99] transition-all duration-300 ".concat(m?"bg-[#f8f8f5] h-16 shadow-sm":"bg-transparent h-24 py-4"),children:(0,l.jsxs)("div",{className:"container mx-auto px-4 h-full flex items-center justify-between",children:[(0,l.jsx)(r.default,{href:"/",className:"font-serif text-2xl font-bold text-[#2c2c27] relative z-10",children:(0,l.jsx)(i.default,{src:"/logo.PNG",alt:"Ankkor",width:160,height:50,priority:!0,className:"".concat(m?"h-10":"h-14"," w-auto transition-all duration-300")})}),(0,l.jsxs)("div",{className:"hidden md:flex items-center space-x-12 relative z-10",children:[(0,l.jsxs)("div",{className:"group relative",children:[(0,l.jsx)("button",{className:"text-[#2c2c27] text-sm uppercase tracking-wider hover:text-[#8a8778] transition-colors py-2 px-1 flex items-center",children:"Collections"}),(0,l.jsx)("div",{className:"absolute left-1/2 transform -translate-x-1/2 pt-2 w-[220px] opacity-0 invisible translate-y-1 group-hover:translate-y-0 group-hover:opacity-100 group-hover:visible transition-all duration-200 ease-out",children:(0,l.jsxs)("div",{className:"bg-[#f8f8f5] border border-[#e5e2d9] rounded-md shadow-md p-2",children:[(0,l.jsx)(r.default,{href:"/collection/shirts",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded",children:"Shirts"}),(0,l.jsx)("div",{className:"h-px bg-[#e5e2d9] my-2"}),(0,l.jsx)(r.default,{href:"/collection",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded",children:"View All Collections"})]})})]}),(0,l.jsx)(r.default,{href:"/about",className:"text-[#2c2c27] text-sm uppercase tracking-wider hover:text-[#8a8778] transition-colors py-2 px-1",children:"Heritage"}),(0,l.jsx)(r.default,{href:"/customer-service",className:"text-[#2c2c27] text-sm uppercase tracking-wider hover:text-[#8a8778] transition-colors py-2 px-1",children:"Customer Service"})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-6 relative z-10",children:[(0,l.jsx)("button",{onClick:()=>C(!0),className:"text-[#2c2c27] hover:text-[#8a8778] transition-colors p-2","aria-label":"Search",children:(0,l.jsx)(c.Z,{className:"h-5 w-5"})}),(0,l.jsxs)("div",{className:"group relative",children:[(0,l.jsx)("button",{className:"text-[#2c2c27] hover:text-[#8a8778] transition-colors p-2",children:(0,l.jsx)(o.Z,{className:"h-5 w-5"})}),(0,l.jsx)("div",{className:"absolute right-0 pt-2 w-[220px] opacity-0 invisible translate-y-1 group-hover:translate-y-0 group-hover:opacity-100 group-hover:visible transition-all duration-200 ease-out",children:(0,l.jsx)("div",{className:"bg-[#f8f8f5] border border-[#e5e2d9] rounded-md shadow-md p-2",children:E?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"px-4 py-1.5 text-sm text-[#5c5c52]",children:["Hello, ",(null==S?void 0:S.firstName)||"there"]}),(0,l.jsx)("div",{className:"h-px bg-[#e5e2d9] my-2"}),(0,l.jsx)(r.default,{href:"/account",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded",children:"My Account"}),(0,l.jsx)(r.default,{href:"/account?tab=orders",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded",children:"Order History"}),(0,l.jsx)(r.default,{href:"/wishlist",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded",children:"Wishlist"}),(0,l.jsx)("div",{className:"h-px bg-[#e5e2d9] my-2"}),(0,l.jsxs)("button",{onClick:Z,className:"flex items-center text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer w-full py-2 px-4 rounded",children:[(0,l.jsx)(n.Z,{className:"h-4 w-4 mr-2"}),"Sign Out"]})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(r.default,{href:"/sign-in",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded",children:"Sign In"}),(0,l.jsx)(r.default,{href:"/sign-up",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded",children:"Create Account"})]})})})]}),(0,l.jsxs)(r.default,{href:"/wishlist",className:"text-[#2c2c27] hover:text-[#8a8778] transition-colors p-2 relative",children:[(0,l.jsx)(d.Z,{className:"h-5 w-5"}),f>0&&(0,l.jsx)("span",{className:"absolute -top-1 -right-1 bg-[#2c2c27] text-[#f4f3f0] text-xs rounded-full h-5 w-5 flex items-center justify-center",children:f})]}),(0,l.jsxs)("button",{onClick:t=>{t.preventDefault(),t.stopPropagation(),e()},className:"text-[#2c2c27] hover:text-[#8a8778] transition-colors relative p-2","aria-label":"Shopping cart",children:[(0,l.jsx)(h.Z,{className:"h-5 w-5"}),t>0&&(0,l.jsx)("span",{className:"absolute -top-1 -right-1 bg-[#2c2c27] text-[#f4f3f0] text-xs rounded-full h-5 w-5 flex items-center justify-center",children:t})]}),(0,l.jsxs)(p,{children:[(0,l.jsx)(v,{asChild:!0,children:(0,l.jsx)("button",{className:"md:hidden text-[#2c2c27] hover:text-[#8a8778] transition-colors","aria-label":"Menu",children:(0,l.jsx)(x.Z,{className:"h-6 w-6"})})}),(0,l.jsx)(N,{side:"right",className:"bg-[#f8f8f5] w-[300px] p-0",children:(0,l.jsxs)("div",{className:"flex flex-col h-full",children:[(0,l.jsx)("div",{className:"p-6 border-b border-[#e5e2d9]",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"font-serif text-xl font-bold text-[#2c2c27]",children:(0,l.jsx)(i.default,{src:"/logo.PNG",alt:"Ankkor",width:120,height:40,className:"h-10 w-auto"})}),(0,l.jsx)(j,{asChild:!0,children:(0,l.jsx)("button",{className:"text-[#2c2c27]","aria-label":"Close menu",children:(0,l.jsx)(u.Z,{className:"h-5 w-5"})})})]})}),(0,l.jsx)("div",{className:"flex-1 overflow-auto py-6 px-6",children:(0,l.jsxs)("nav",{className:"space-y-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-serif text-sm uppercase tracking-wider text-[#8a8778] mb-3",children:"Collections"}),(0,l.jsxs)("ul",{className:"space-y-3",children:[(0,l.jsx)("li",{children:(0,l.jsx)(j,{asChild:!0,children:(0,l.jsx)(r.default,{href:"/collection/shirts",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Shirts"})})}),(0,l.jsx)("li",{children:(0,l.jsx)(j,{asChild:!0,children:(0,l.jsx)(r.default,{href:"/collection",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"View All Collections"})})})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-serif text-sm uppercase tracking-wider text-[#8a8778] mb-3",children:"Company"}),(0,l.jsxs)("ul",{className:"space-y-3",children:[(0,l.jsx)("li",{children:(0,l.jsx)(j,{asChild:!0,children:(0,l.jsx)(r.default,{href:"/about",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Heritage"})})}),(0,l.jsx)("li",{children:(0,l.jsx)(j,{asChild:!0,children:(0,l.jsx)(r.default,{href:"/customer-service",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Customer Service"})})})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-serif text-sm uppercase tracking-wider text-[#8a8778] mb-3",children:"Account"}),(0,l.jsx)("ul",{className:"space-y-3",children:E?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("li",{children:(0,l.jsx)(j,{asChild:!0,children:(0,l.jsx)(r.default,{href:"/account",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"My Account"})})}),(0,l.jsx)("li",{children:(0,l.jsx)(j,{asChild:!0,children:(0,l.jsx)(r.default,{href:"/account?tab=orders",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Order History"})})}),(0,l.jsx)("li",{children:(0,l.jsx)(j,{asChild:!0,children:(0,l.jsx)(r.default,{href:"/wishlist",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Wishlist"})})}),(0,l.jsx)("li",{children:(0,l.jsx)(j,{asChild:!0,children:(0,l.jsx)("button",{onClick:Z,className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Sign Out"})})})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("li",{children:(0,l.jsx)(j,{asChild:!0,children:(0,l.jsx)(r.default,{href:"/sign-in",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Sign In"})})}),(0,l.jsx)("li",{children:(0,l.jsx)(j,{asChild:!0,children:(0,l.jsx)(r.default,{href:"/sign-up",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Create Account"})})})]})})]})]})})]})})]}),!E&&(0,l.jsxs)(r.default,{href:"/sign-up",className:"hidden md:flex items-center ml-3 bg-gradient-to-b from-[#2c2c27] to-[#3a3a34] text-[#f8f8f5] border border-[#2c2c27] font-medium text-sm uppercase tracking-wider px-6 py-2.5 rounded-sm shadow-sm hover:shadow-md hover:from-[#3a3a34] hover:to-[#2c2c27] transition-all duration-200",children:[(0,l.jsx)(o.Z,{className:"h-4 w-4 mr-2 opacity-80"}),"Sign Up"]})]})]})}),(0,l.jsx)("div",{className:m?"h-16":"h-20"}),(0,l.jsx)(A,{isOpen:g,onClose:()=>C(!1)})]})},L=s(64528),P=()=>{let[e,t]=(0,a.useState)(!1),{isLaunchingSoon:s}=(0,L.Gd)();return((0,a.useEffect)(()=>{t(!0)},[]),!e||s)?null:(0,l.jsx)(z,{})}}}]);