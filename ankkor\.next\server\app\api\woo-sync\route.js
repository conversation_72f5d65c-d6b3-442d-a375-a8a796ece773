"use strict";(()=>{var e={};e.id=7536,e.ids=[7536],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},93690:e=>{e.exports=import("graphql-request")},72254:e=>{e.exports=require("node:buffer")},6005:e=>{e.exports=require("node:crypto")},47261:e=>{e.exports=require("node:util")},39082:(e,t,o)=>{o.a(e,async(e,r)=>{try{o.r(t),o.d(t,{originalPathname:()=>g,patchFetch:()=>l,requestAsyncStorage:()=>p,routeModule:()=>u,serverHooks:()=>y,staticGenerationAsyncStorage:()=>d});var n=o(49303),s=o(88716),a=o(60670),c=o(42004),i=e([c]);c=(i.then?(await i)():i)[0];let u=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/woo-sync/route",pathname:"/api/woo-sync",filename:"route",bundlePath:"app/api/woo-sync/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\woo-sync\\route.ts",nextConfigOutput:"",userland:c}),{requestAsyncStorage:p,staticGenerationAsyncStorage:d,serverHooks:y}=u,g="/api/woo-sync/route";function l(){return(0,a.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:d})}r()}catch(e){r(e)}})},42004:(e,t,o)=>{o.a(e,async(e,r)=>{try{o.r(t),o.d(t,{POST:()=>S});var n=o(87070),s=o(19910),a=o(92861),c=o(79534),i=e([s]);s=(i.then?(await i)():i)[0];let g={PRODUCTS:3600,CATEGORIES:86400};async function l(e){try{let t=await e.json();if(t.token!==process.env.WOOCOMMERCE_REVALIDATION_SECRET)return n.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let o=t.type||"inventory";switch(console.log(`Starting WooCommerce ${o} sync at ${new Date().toISOString()}`),o){case"inventory":await u();break;case"all":await p();break;case"categories":await d();break;default:return n.NextResponse.json({success:!1,error:"Invalid sync type"},{status:400})}return n.NextResponse.json({success:!0,message:`Successfully completed WooCommerce ${o} sync`,timestamp:new Date().toISOString()})}catch(e){return console.error("Error during WooCommerce sync:",e),n.NextResponse.json({success:!1,error:"Sync operation failed"},{status:500})}}async function u(){console.log("Syncing WooCommerce inventory...");try{let e=await s.Dg(100);if(!e||0===e.length){console.log("No products found to sync inventory");return}console.log(`Found ${e.length} products to sync inventory`);let t=e.map(e=>({productId:e.databaseId.toString(),productSlug:e.slug}));for(let o of(await a.p_(t),e)){let e=`product:${o.slug}`,t=await c.U2(e);if(t){let r={...t,availableForSale:"IN_STOCK"===o.stockStatus||o.variations?.nodes?.some(e=>"IN_STOCK"===e.stockStatus),_lastInventoryUpdate:new Date().toISOString()};await c.t8(e,r,g.PRODUCTS)}}console.log(`Successfully updated inventory for ${e.length} products`)}catch(e){throw console.error("Error syncing WooCommerce inventory:",e),e}}async function p(){console.log("Syncing all WooCommerce products...");try{let e=await s.Dg(100);if(!e||0===e.length){console.log("No products found to sync");return}console.log(`Found ${e.length} products to sync`);let t=e.map(e=>({productId:e.databaseId.toString(),productSlug:e.slug}));for(let o of(await a.p_(t),e)){let e=s.Op(o);if(e){let t=`product:${e.handle}`;await c.t8(t,e,g.PRODUCTS)}}await c.IV("all_products"),await c.IV("featured_products"),console.log(`Successfully synced ${e.length} products`)}catch(e){throw console.error("Error syncing all WooCommerce products:",e),e}}async function d(){console.log("Syncing WooCommerce categories...");try{let e=await s.tG(50);if(!e||0===e.length){console.log("No categories found to sync");return}for(let t of(console.log(`Found ${e.length} categories to sync`),e)){let e=s.Zh(t);if(e){let t=`category:${e.handle}`;await c.t8(t,e,g.CATEGORIES)}}let t=e.map(s.Zh).filter(Boolean);await c.t8("all_categories",t,g.CATEGORIES),console.log(`Successfully synced ${e.length} categories`)}catch(e){throw console.error("Error syncing WooCommerce categories:",e),e}}async function y(){if(process.env.QSTASH_CURRENT_SIGNING_KEY)try{let{verifySignature:e}=await Promise.all([o.e(926),o.e(7653)]).then(o.bind(o,67653));return e(l)}catch(e){console.warn("Failed to initialize QStash verification, falling back to direct handler:",e)}return l}let S=async e=>(await y())(e);r()}catch(e){r(e)}})},79534:(e,t,o)=>{o.d(t,{Fs:()=>l,IV:()=>i,Pc:()=>s,U2:()=>a,mJ:()=>n,t8:()=>c});let r=new(o(94868)).s({url:process.env.UPSTASH_REDIS_REST_URL||process.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""}),n={SHORT:300,MEDIUM:3600,LONG:86400,WEEK:604800};function s(){return!!(process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_URL&&process.env.NEXT_PUBLIC_KV_REST_API_TOKEN)}async function a(e){if(!s())return null;try{return await r.get(e)||null}catch(t){return console.error(`Redis get error for key ${e}:`,t),null}}async function c(e,t,o){if(!s())return!1;try{return o?await r.setex(e,o,t):await r.set(e,t),!0}catch(t){return console.error(`Redis set error for key ${e}:`,t),!1}}async function i(e){if(!s())return!1;try{return await r.del(e),!0}catch(t){return console.error(`Redis delete error for key ${e}:`,t),!1}}async function l(e,t,o=n.MEDIUM){if(!s())return await t();try{let n=await r.get(e);if(null!==n)return console.log(`Cache hit for key: ${e}`),JSON.parse(n);console.log(`Cache miss for key: ${e}, fetching fresh data`);let s=await t();return await r.setex(e,o,JSON.stringify(s)),s}catch(o){return console.error(`Redis cache error for key ${e}:`,o),await t()}}}};var t=require("../../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),r=t.X(0,[8948,5972,4766,4868,9910],()=>o(39082));module.exports=r})();