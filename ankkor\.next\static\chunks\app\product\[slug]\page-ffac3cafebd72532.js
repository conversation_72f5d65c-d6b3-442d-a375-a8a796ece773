(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1599],{54955:function(e,t,s){Promise.resolve().then(s.bind(s,46782))},46782:function(e,t,s){"use strict";s.d(t,{default:function(){return p}});var l=s(57437),a=s(2265),c=s(33145),r=s(43886),n=s(87758),o=s(12381),i=s(18686),d=s(21047),u=s(99397),x=s(42449),p=e=>{var t;let{product:s}=e,[p,m]=(0,a.useState)(0),[v,h]=(0,a.useState)(1),[g,f]=(0,a.useState)(null),[j,N]=(0,a.useState)({}),[y,S]=(0,a.useState)(!1),b=(0,n.useLocalCartStore)(),{openCart:k}=(0,i.j)(),{id:w,databaseId:C,name:T,description:U,shortDescription:_,price:E,regularPrice:I,onSale:L,stockStatus:A,image:O,galleryImages:q,attributes:D,type:M,variations:Q}=s,{stockData:R,isConnected:z}=function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],[s,l]=(0,a.useState)({}),{isConnected:c,error:r}=function(){let{productIds:e=[],onStockUpdate:t,enabled:s=!0}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[l,c]=(0,a.useState)(!1),[r,n]=(0,a.useState)(null),[o,i]=(0,a.useState)(null),d=(0,a.useRef)(null),u=(0,a.useRef)(null),x=(0,a.useMemo)(()=>e,[e.join(",")]),p=(0,a.useCallback)(e=>{null==t||t(e)},[t]),m=(0,a.useCallback)(()=>{if(d.current&&(d.current.close(),d.current=null),u.current&&(clearTimeout(u.current),u.current=null),!s||0===x.length)return console.log("Stock updates disabled or no products specified"),null;let e=new URLSearchParams({products:x.join(",")});console.log("Creating new stock updates connection for products:",x);let t=new EventSource("/api/stock-updates?".concat(e));return d.current=t,t.onopen=()=>{console.log("Stock updates stream connected for products:",x),c(!0),i(null)},t.onmessage=e=>{try{let t=JSON.parse(e.data);n(t),"stock_update"===t.type&&(console.log("Stock update received:",t),p(t))}catch(e){console.error("Error parsing stock update:",e)}},t.onerror=e=>{console.error("Stock updates stream error:",e),c(!1),i("Connection to stock updates failed"),d.current===t&&(u.current=setTimeout(()=>{d.current===t&&t.readyState===EventSource.CLOSED&&(console.log("Attempting to reconnect stock updates..."),m())},5e3))},t},[x,p,s]);return(0,a.useEffect)(()=>(m(),()=>{d.current&&(console.log("Cleaning up stock updates connection"),d.current.close(),d.current=null,c(!1)),u.current&&(clearTimeout(u.current),u.current=null)}),[m]),{isConnected:l,lastUpdate:r,error:o,reconnect:m}}({productIds:(0,a.useMemo)(()=>e?[e]:[],[e]),onStockUpdate:(0,a.useCallback)(t=>{t.productId===e&&l(e=>({...e,stockStatus:t.stockStatus,stockQuantity:t.stockQuantity,availableForSale:t.availableForSale,lastUpdated:t.timestamp}))},[e]),enabled:t&&!!e});return{stockData:s,isConnected:c,error:r}}((null==C?void 0:C.toString())||"",!0),P=R.stockStatus||A,Z=R.stockQuantity,B="VARIABLE"===M,F=[(null==O?void 0:O.sourceUrl)?{sourceUrl:O.sourceUrl,altText:O.altText||T}:null,...(null==q?void 0:q.nodes)||[]].filter(Boolean),H=(e,t)=>{if(N(s=>({...s,[e]:t})),B&&(null==Q?void 0:Q.nodes)){var s;let l={...j,[e]:t};if(null==D?void 0:null===(s=D.nodes)||void 0===s?void 0:s.every(e=>l[e.name])){let e=Q.nodes.find(e=>e.attributes.nodes.every(e=>{let t=l[e.name];return e.value===t}));e?f(e):f(null)}}},$=async()=>{S(!0);try{var e,t;let s={productId:C.toString(),quantity:v,name:T,price:(null==g?void 0:g.price)||E,image:{url:(null===(e=F[0])||void 0===e?void 0:e.sourceUrl)||"",altText:(null===(t=F[0])||void 0===t?void 0:t.altText)||T}};await b.addToCart(s),k()}catch(e){console.error("Error adding product to cart:",e)}finally{S(!1)}},J="IN_STOCK"!==(P||A)&&"instock"!==(P||A),K=!B||B&&g;return(0,l.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsx)("div",{className:"relative aspect-square bg-[#f4f3f0] overflow-hidden",children:(null===(t=F[p])||void 0===t?void 0:t.sourceUrl)&&(0,l.jsx)(c.default,{src:F[p].sourceUrl,alt:F[p].altText||T,fill:!0,sizes:"(max-width: 768px) 100vw, 50vw",priority:!0,className:"object-cover"})}),F.length>1&&(0,l.jsx)("div",{className:"grid grid-cols-5 gap-2",children:F.map((e,t)=>(0,l.jsx)("button",{onClick:()=>m(t),className:"relative aspect-square bg-[#f4f3f0] ".concat(p===t?"ring-2 ring-[#2c2c27]":""),children:(0,l.jsx)(c.default,{src:e.sourceUrl,alt:e.altText||"".concat(T," - Image ").concat(t+1),fill:!0,sizes:"(max-width: 768px) 20vw, 10vw",className:"object-cover"})},t))})]}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsx)("h1",{className:"text-3xl font-serif text-[#2c2c27]",children:T}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("span",{className:"text-xl font-medium text-[#2c2c27]",children:((null==g?void 0:g.price)||E).toString().includes("₹")||((null==g?void 0:g.price)||E).toString().includes("$")||((null==g?void 0:g.price)||E).toString().includes("€")||((null==g?void 0:g.price)||E).toString().includes("\xa3")?(null==g?void 0:g.price)||E:"₹".concat((null==g?void 0:g.price)||E)}),L&&I&&(0,l.jsx)("span",{className:"text-sm line-through text-[#8a8778]",children:I.toString().includes("₹")||I.toString().includes("$")||I.toString().includes("€")||I.toString().includes("\xa3")?I:"₹".concat(I)})]}),_&&(0,l.jsx)("div",{className:"prose prose-sm text-[#5c5c52]",dangerouslySetInnerHTML:{__html:_}}),B&&(null==D?void 0:D.nodes)&&(0,l.jsx)("div",{className:"space-y-4",children:D.nodes.map(e=>(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("h3",{className:"font-medium text-[#2c2c27]",children:e.name}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:e.options.map(t=>(0,l.jsx)("button",{onClick:()=>H(e.name,t),className:"px-4 py-2 border ".concat(j[e.name]===t?"border-[#2c2c27] bg-[#2c2c27] text-white":"border-gray-300 hover:border-[#8a8778]"),children:t},t))})]},e.name))}),(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)("span",{className:"text-[#5c5c52]",children:"Quantity:"}),(0,l.jsxs)("div",{className:"flex items-center border border-gray-300",children:[(0,l.jsx)("button",{onClick:()=>h(e=>e>1?e-1:1),disabled:v<=1,className:"px-3 py-2 hover:bg-gray-100","aria-label":"Decrease quantity",children:(0,l.jsx)(d.Z,{className:"h-4 w-4"})}),(0,l.jsx)("span",{className:"px-4 py-2 border-x border-gray-300",children:v}),(0,l.jsx)("button",{onClick:()=>h(e=>e+1),className:"px-3 py-2 hover:bg-gray-100","aria-label":"Increase quantity",children:(0,l.jsx)(u.Z,{className:"h-4 w-4"})})]})]}),(0,l.jsxs)("div",{className:"text-sm",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("span",{className:"font-medium",children:"Availability: "}),(0,l.jsx)("span",{className:J?"text-red-600":"text-green-600",children:J?"Out of Stock":"In Stock"})]}),null!=Z&&(0,l.jsx)("div",{className:"text-xs text-gray-600 mt-1",children:Z>0?(0,l.jsxs)("span",{children:[Z," items available"]}):(0,l.jsx)("span",{className:"text-red-600",children:"No items in stock"})}),R.lastUpdated&&(0,l.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Last updated: ",new Date(R.lastUpdated).toLocaleTimeString()]})]}),(0,l.jsxs)(r.E.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},children:[(0,l.jsxs)(o.z,{onClick:$,disabled:J||y||!K,className:"w-full py-6 bg-[#2c2c27] text-white hover:bg-[#3c3c37] flex items-center justify-center gap-2",children:[(0,l.jsx)(x.Z,{className:"h-5 w-5"}),y?"Adding...":"Add to Cart"]}),B&&!K&&!J&&(0,l.jsx)("p",{className:"mt-2 text-sm text-red-600",children:"Please select all options to add this product to your cart"})]}),U&&(0,l.jsxs)("div",{className:"mt-12 border-t border-gray-200 pt-8",children:[(0,l.jsx)("h2",{className:"text-xl font-serif mb-4 text-[#2c2c27]",children:"Description"}),(0,l.jsx)("div",{className:"prose prose-sm text-[#5c5c52]",dangerouslySetInnerHTML:{__html:U}})]})]})]})})}}},function(e){e.O(0,[5540,4822,8787,8261,1539,2188,6003,7231,2323,9960,7696,8002,7111,8989,4513,8476,8496,8966,3903,6076,4596,62,6271,8726,8133,8049,2870,632,6613,9965,5270,2647,9971,9290,6459,7800,4818,1729,5526,4615,7406,2532,2068,4595,3280,8790,1104,7158,7044,9482,6628,986,4754,1744],function(){return e(e.s=54955)}),_N_E=e.O()}]);