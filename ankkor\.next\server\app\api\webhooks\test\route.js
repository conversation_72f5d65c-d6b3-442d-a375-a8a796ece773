"use strict";(()=>{var e={};e.id=5165,e.ids=[5165],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},60471:(e,t,o)=>{o.r(t),o.d(t,{originalPathname:()=>h,patchFetch:()=>k,requestAsyncStorage:()=>d,routeModule:()=>p,serverHooks:()=>g,staticGenerationAsyncStorage:()=>l});var s={};o.r(s),o.d(s,{GET:()=>u,POST:()=>c});var r=o(49303),n=o(88716),a=o(60670),i=o(87070);async function c(e){try{let t=await e.text(),s=e.headers.get("x-wc-webhook-signature");console.log("=== Webhook Test Endpoint ==="),console.log("Headers:",Object.fromEntries(e.headers.entries())),console.log("Body:",t),console.log("Signature:",s);let r=process.env.WOOCOMMERCE_WEBHOOK_SECRET;if(console.log("Webhook secret configured:",!!r),r&&s){let e=o(84770).createHmac("sha256",r).update(t,"utf8").digest("base64");console.log("Expected signature:",e),console.log("Received signature:",s),console.log("Signatures match:",s===e)}return i.NextResponse.json({success:!0,message:"Test webhook received",hasSignature:!!s,hasSecret:!!r,bodyLength:t.length})}catch(e){return console.error("Test webhook error:",e),i.NextResponse.json({error:"Test webhook failed"},{status:500})}}async function u(){return i.NextResponse.json({message:"Webhook test endpoint",status:"active",secretConfigured:!!process.env.WOOCOMMERCE_WEBHOOK_SECRET})}let p=new r.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/webhooks/test/route",pathname:"/api/webhooks/test",filename:"route",bundlePath:"app/api/webhooks/test/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\webhooks\\test\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:d,staticGenerationAsyncStorage:l,serverHooks:g}=p,h="/api/webhooks/test/route";function k(){return(0,a.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:l})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),s=t.X(0,[8948,5972],()=>o(60471));module.exports=s})();