"use strict";(()=>{var e={};e.id=3311,e.ids=[3311],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},75545:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>S,patchFetch:()=>E,requestAsyncStorage:()=>d,routeModule:()=>p,serverHooks:()=>f,staticGenerationAsyncStorage:()=>v});var o={};t.r(o),t.d(o,{GET:()=>u,POST:()=>l});var n=t(49303),a=t(88716),s=t(60670),i=t(87070),c=t(95307);async function u(){try{let e=await (0,c.on)();return i.NextResponse.json({success:!0,message:`Cleaned up ${e} expired reservations`,cleanedUp:e,timestamp:new Date().toISOString()})}catch(e){return console.error("Error in cleanup API:",e),i.NextResponse.json({error:"Cleanup failed"},{status:500})}}async function l(e){try{let r=e.headers.get("authorization"),t=process.env.CLEANUP_SECRET_TOKEN||"cleanup_secret_token";if(r!==`Bearer ${t}`)return i.NextResponse.json({error:"Unauthorized"},{status:401});let o=await (0,c.on)();return console.log(`Scheduled cleanup completed: ${o} reservations removed`),i.NextResponse.json({success:!0,message:`Scheduled cleanup completed: ${o} reservations removed`,cleanedUp:o,timestamp:new Date().toISOString()})}catch(e){return console.error("Error in scheduled cleanup:",e),i.NextResponse.json({error:"Scheduled cleanup failed"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/reservations/cleanup/route",pathname:"/api/reservations/cleanup",filename:"route",bundlePath:"app/api/reservations/cleanup/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\reservations\\cleanup\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:d,staticGenerationAsyncStorage:v,serverHooks:f}=p,S="/api/reservations/cleanup/route";function E(){return(0,s.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:v})}},95307:(e,r,t)=>{t.d(r,{O$:()=>v,ag:()=>p,eI:()=>c,fr:()=>d,mV:()=>u,on:()=>S});var o=t(94868);let n=process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN?new o.s({url:process.env.UPSTASH_REDIS_REST_URL,token:process.env.UPSTASH_REDIS_REST_TOKEN}):null,a={DURATION_MINUTES:15,MAX_RESERVATIONS_PER_USER:10};function s(e,r){return r?`reservation:product:${e}:variation:${r}`:`reservation:product:${e}`}function i(e){return`user_reservations:${e}`}async function c(e,r,t,o,c){if(!n)return{success:!1,error:"Reservation service unavailable"};try{if((await p(t)).length>=a.MAX_RESERVATIONS_PER_USER)return{success:!1,error:"Too many active reservations"};let l=await u(e,o);if(!l.success||(l.availableStock||0)<r)return{success:!1,error:"Insufficient stock available",availableStock:l.availableStock};let d=new Date,v=new Date(d.getTime()+6e4*a.DURATION_MINUTES),f={id:`res_${Date.now()}_${Math.random().toString(36).substring(2,15)}`,productId:e,variationId:o,quantity:r,userId:t,reservedAt:d.toISOString(),expiresAt:v.toISOString(),status:"active",cartId:c},S=`${s(e,o)}:${f.id}`;await n.set(S,f,{ex:60*a.DURATION_MINUTES});let E=i(t);return await n.sadd(E,f.id),await n.expire(E,60*a.DURATION_MINUTES),console.log(`Created stock reservation: ${f.id} for product ${e} (${r} items)`),{success:!0,reservation:f}}catch(e){return console.error("Error creating stock reservation:",e),{success:!1,error:"Failed to create reservation"}}}async function u(e,r){if(!n)return{success:!1};try{let t=await f(e,r);if(null===t)return{success:!1};let o=await l(e,r);return{success:!0,availableStock:Math.max(0,t-o),totalStock:t,reservedStock:o}}catch(e){return console.error("Error checking available stock:",e),{success:!1}}}async function l(e,r){if(!n)return 0;try{let t=`${s(e,r)}:*`,o=await n.keys(t),a=0;for(let e of o){let r=await n.get(e);r&&"active"===r.status&&(a+=r.quantity)}return a}catch(e){return console.error("Error getting reserved stock:",e),0}}async function p(e){if(!n)return[];try{let r=i(e),t=await n.smembers(r),o=[];for(let e of t)for(let r of(await n.keys(`reservation:product:*:${e}`))){let e=await n.get(r);e&&"active"===e.status&&o.push(e)}return o}catch(e){return console.error("Error getting user reservations:",e),[]}}async function d(e){if(!n)return!1;try{for(let r of(await n.keys(`reservation:product:*:${e}`))){let t=await n.get(r);if(t)return t.status="confirmed",await n.set(r,t,{ex:86400}),console.log(`Confirmed reservation: ${e}`),!0}return!1}catch(e){return console.error("Error confirming reservation:",e),!1}}async function v(e){if(!n)return!1;try{for(let r of(await n.keys(`reservation:product:*:${e}`)))await n.del(r),console.log(`Released reservation: ${e}`);return!0}catch(e){return console.error("Error releasing reservation:",e),!1}}async function f(e,r){try{let t=`https://ankkorwoo.vercel.app/api/products/${e}/stock${r?`?variation_id=${r}`:""}`,o=await fetch(t);if(!o.ok)return null;return(await o.json()).stockQuantity||0}catch(e){return console.error("Error fetching stock from WooCommerce:",e),null}}async function S(){if(!n)return 0;try{let e=await n.keys("reservation:product:*"),r=0;for(let t of e){let e=await n.get(t);e&&new Date(e.expiresAt)<new Date&&(await n.del(t),r++)}return console.log(`Cleaned up ${r} expired reservations`),r}catch(e){return console.error("Error cleaning up expired reservations:",e),0}}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,5972,4766,4868],()=>t(75545));module.exports=o})();