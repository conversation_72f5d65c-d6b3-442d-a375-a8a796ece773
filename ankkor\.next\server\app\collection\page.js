(()=>{var e={};e.id=1306,e.ids=[1306],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},11443:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.Z,__next_app__:()=>m,originalPathname:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>l}),r(32606),r(11360),r(7629),r(11930),r(12523);var n=r(23191),s=r(88716),a=r(43315),o=r(95231),i={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);r.d(t,i);let l=["",{children:["collection",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,32606)),"E:\\ankkorwoo\\ankkor\\src\\app\\collection\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,11360)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,7629)),"E:\\ankkorwoo\\ankkor\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,11930)),"E:\\ankkorwoo\\ankkor\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],c=["E:\\ankkorwoo\\ankkor\\src\\app\\collection\\page.tsx"],d="/collection/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/collection/page",pathname:"/collection",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},33074:(e,t,r)=>{Promise.resolve().then(r.bind(r,90754))},41137:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(76557).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},46226:(e,t,r)=>{"use strict";r.d(t,{default:()=>s.a});var n=r(69029),s=r.n(n)},69029:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return i}});let n=r(91174),s=r(23078),a=r(92481),o=n._(r(86820));function i(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=a.Image},90754:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{default:()=>x});var s=r(10326),a=r(17577),o=r(46226),i=r(41137),l=r(94019),c=r(53471),d=r(15725),m=r(9512),p=r(45107),u=r(38227),g=e([c,d]);[c,d]=g.then?(await g)():g;let h=[{id:"all",name:"All Categories"}],f=[{id:"newest",name:"Newest"},{id:"featured",name:"Featured"},{id:"price-asc",name:"Price: Low to High"},{id:"price-desc",name:"Price: High to Low"},{id:"rating",name:"Alphabetical"}];function x(){let[e,t]=(0,a.useState)([]),[r,n]=(0,a.useState)(!0),[d,g]=(0,a.useState)(null),[x,v]=(0,a.useState)("all"),[y,b]=(0,a.useState)("newest"),[j,w]=(0,a.useState)(!1);(0,m.Z)(r,"fabric");let k=e=>{let t=(e.collections||[]).map(e=>e.handle?.toLowerCase()||""),r=e._originalWooProduct,n=r?.type?.toLowerCase()||"",s=(r?.productCategories?.nodes||[]).map(e=>e.name?.toLowerCase()||"");return t.some(e=>e.includes("shirt"))||s.some(e=>e.includes("shirt"))||n.includes("shirt")?"shirts":t.some(e=>e.includes("polo"))||s.some(e=>e.includes("polo"))||n.includes("polo")?"polos":"other"},N=[..."all"===x?e:e.filter(e=>k(e)===x)].sort((e,t)=>{switch(y){case"price-asc":let r=parseFloat(e.priceRange?.minVariantPrice?.amount||"0"),n=parseFloat(t.priceRange?.minVariantPrice?.amount||"0");return r-n;case"price-desc":let s=parseFloat(e.priceRange?.minVariantPrice?.amount||"0");return parseFloat(t.priceRange?.minVariantPrice?.amount||"0")-s;case"rating":return e.title.localeCompare(t.title);case"newest":let a=e._originalWooProduct?.dateCreated||e._originalWooProduct?.date_created||e.id,o=t._originalWooProduct?.dateCreated||t._originalWooProduct?.date_created||t.id;if(a&&o&&a!==e.id&&o!==t.id)return new Date(o).getTime()-new Date(a).getTime();return t.id.localeCompare(e.id);default:return 0}});return(0,s.jsxs)("div",{className:"min-h-screen bg-[#f8f8f5] pt-8 pb-24",children:[s.jsx("div",{className:"container mx-auto px-4 mb-12",children:(0,s.jsxs)("div",{className:"text-center max-w-3xl mx-auto",children:[s.jsx("h1",{className:"text-4xl font-serif font-bold mb-4 text-[#2c2c27]",children:"The Collection"}),s.jsx("p",{className:"text-[#5c5c52] mb-8",children:"Discover our curated selection of timeless menswear essentials, crafted with exceptional materials and meticulous attention to detail."})]})}),(0,s.jsxs)("div",{className:"relative h-[300px] mb-16 overflow-hidden",children:[s.jsx(o.default,{src:"https://images.unsplash.com/photo-1441984904996-e0b6ba687e04?q=80",alt:"Ankkor Collection",fill:!0,sizes:"(max-width: 768px) 100vw, 50vw",className:"object-cover image-animate"}),s.jsx("div",{className:"absolute inset-0 bg-[#2c2c27] bg-opacity-30 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center text-white",children:[s.jsx("h2",{className:"text-3xl font-serif font-bold mb-4",children:"Spring/Summer 2025"}),s.jsx("p",{className:"text-lg max-w-xl mx-auto",children:"Timeless elegance for the modern gentleman"})]})})]}),(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[d&&!r&&s.jsx("div",{className:"text-center py-16",children:(0,s.jsxs)("div",{className:"max-w-md mx-auto",children:[s.jsx("h3",{className:"text-xl font-serif text-[#2c2c27] mb-4",children:"Service Temporarily Unavailable"}),s.jsx("p",{className:"text-[#5c5c52] mb-6",children:d}),s.jsx("button",{onClick:()=>window.location.reload(),className:"bg-[#2c2c27] text-white px-6 py-2 hover:bg-[#3d3d35] transition-colors",children:"Try Again"})]})}),(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8 md:hidden",children:[(0,s.jsxs)("button",{onClick:()=>w(!0),className:"flex items-center gap-2 text-[#2c2c27] border border-[#e5e2d9] px-4 py-2",children:[s.jsx(i.Z,{className:"h-4 w-4"}),s.jsx("span",{children:"Filter & Sort"})]}),(0,s.jsxs)("div",{className:"text-[#5c5c52] text-sm",children:[N.length," products"]})]}),j&&(0,s.jsxs)("div",{className:"fixed inset-0 z-50 md:hidden",children:[s.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:()=>w(!1)}),(0,s.jsxs)("div",{className:"absolute right-0 top-0 bottom-0 w-80 bg-[#f8f8f5] p-6 overflow-auto",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[s.jsx("h3",{className:"font-serif text-lg text-[#2c2c27]",children:"Filter & Sort"}),s.jsx("button",{onClick:()=>w(!1),children:s.jsx(l.Z,{className:"h-5 w-5 text-[#2c2c27]"})})]}),(0,s.jsxs)("div",{children:[s.jsx("h4",{className:"text-[#8a8778] text-xs uppercase tracking-wider mb-4",children:"Sort By"}),s.jsx("div",{className:"space-y-3",children:f.map(e=>s.jsx("button",{onClick:()=>b(e.id),className:`block w-full text-left py-1 ${y===e.id?"text-[#2c2c27] font-medium":"text-[#5c5c52]"}`,children:e.name},e.id))})]}),s.jsx("button",{onClick:()=>w(!1),className:"w-full bg-[#2c2c27] text-[#f4f3f0] py-3 mt-8 text-sm uppercase tracking-wider",children:"Apply Filters"})]})]}),(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-10",children:[s.jsx("div",{className:"hidden md:block w-64 shrink-0",children:s.jsx("div",{className:"sticky top-24",children:(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"text-[#2c2c27] font-serif text-lg mb-6",children:"Sort By"}),s.jsx("div",{className:"space-y-3",children:f.map(e=>s.jsx("button",{onClick:()=>b(e.id),className:`block w-full text-left py-1 ${y===e.id?"text-[#2c2c27] font-medium":"text-[#5c5c52] hover:text-[#2c2c27] transition-colors"}`,children:e.name},e.id))})]})})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"hidden md:flex justify-between items-center mb-8",children:[s.jsx("h2",{className:"text-[#2c2c27] font-serif text-xl",children:"all"===x?"All Products":h.find(e=>e.id===x)?.name}),(0,s.jsxs)("div",{className:"text-[#5c5c52]",children:[N.length," products"]})]}),r&&s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8",children:Array.from({length:9}).map((e,t)=>(0,s.jsxs)("div",{className:"animate-pulse",children:[s.jsx(u.O,{className:"w-full h-80 mb-4"}),s.jsx(u.O,{className:"w-3/4 h-4 mb-2"}),s.jsx(u.O,{className:"w-1/2 h-4"})]},t))}),!r&&!d&&s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8",children:N.map(e=>{let t="";try{if(e.variants&&e.variants.length>0){let r=e.variants[0];if(r&&r.id&&!(t=r.id).startsWith("gid://woocommerce/ProductVariant/")){let r=t.replace(/\D/g,"");r?t=`gid://woocommerce/ProductVariant/${r}`:(console.warn(`Cannot parse variant ID for product ${e.title}: ${t}`),t="")}}if(!t&&e.id&&e.id.includes("/")){let r=e.id.split("/"),n=r[r.length-1];n&&/^\d+$/.test(n)&&(t=`gid://woocommerce/ProductVariant/${n}1`,console.warn(`Using fallback variant ID for product ${e.title}: ${t}`))}let r=e._originalWooProduct;return s.jsx(c.Z,{id:e.id,name:e.title,slug:e.handle,price:r?.salePrice||r?.price||e.priceRange?.minVariantPrice?.amount||"0",image:e.images[0]?.url||"",isNew:!0,stockStatus:r?.stockStatus||"IN_STOCK",compareAtPrice:e.compareAtPrice,regularPrice:r?.regularPrice,salePrice:r?.salePrice,onSale:r?.onSale||!1,currencySymbol:(0,p.jK)(e.currencyCode),currencyCode:e.currencyCode||"INR",shortDescription:r?.shortDescription,type:r?.type},e.id)}catch(t){return console.error(`Error processing product ${e.title||"unknown"}:`,t),null}})}),!r&&!d&&0===N.length&&s.jsx("div",{className:"text-center py-16",children:s.jsx("p",{className:"text-[#5c5c52]",children:"No products available at the moment."})})]})]})]})]})}n()}catch(e){n(e)}})},38227:(e,t,r)=>{"use strict";r.d(t,{O:()=>a});var n=r(10326),s=r(51223);function a({className:e,...t}){return n.jsx("div",{"data-slot":"skeleton",className:(0,s.cn)("bg-primary/10 animate-pulse rounded-md",e),...t})}},9512:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s}),r(17577);var n=r(2861);let s=function(e,t){let{setLoading:r,setVariant:s}=(0,n.r)()}},92079:(e,t,r)=>{"use strict";r.r(t),r.d(t,{addInventoryMapping:()=>d,clearInventoryMappings:()=>g,getAllInventoryMappings:()=>u,getProductHandleFromInventory:()=>m,loadInventoryMap:()=>l,saveInventoryMap:()=>c,updateInventoryMappings:()=>p});var n=r(78578);let s="inventory:mapping:",a=new n.s({url:process.env.UPSTASH_REDIS_REST_URL||process.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""}),o={};function i(){return!!(process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_URL&&process.env.NEXT_PUBLIC_KV_REST_API_TOKEN)}async function l(){if(!i())return{...o};try{let e=await a.keys(`${s}*`);if(0===e.length)return console.log("No existing inventory mappings found in Redis"),{};let t={},r=await a.mget(...e);return e.forEach((e,n)=>{let a=e.replace(s,""),o=r[n];t[a]=o}),console.log(`Loaded inventory mapping with ${Object.keys(t).length} entries from Redis`),t}catch(e){return console.error("Error loading inventory mapping from Redis:",e),console.log("Falling back to in-memory storage"),{...o}}}async function c(e){if(i())try{let t=a.pipeline(),r=await a.keys(`${s}*`);r.length>0&&t.del(...r),Object.entries(e).forEach(([e,r])=>{t.set(`${s}${e}`,r)}),await t.exec(),console.log(`Saved inventory mapping with ${Object.keys(e).length} entries to Redis`)}catch(t){console.error("Error saving inventory mapping to Redis:",t),console.log("Falling back to in-memory storage"),Object.assign(o,e)}else Object.assign(o,e),console.log(`Saved inventory mapping with ${Object.keys(e).length} entries to memory`)}async function d(e,t){try{return i()?(await a.set(`${s}${e}`,t),console.log(`Added mapping to Redis: ${e} -> ${t}`)):(o[e]=t,console.log(`Added mapping to memory: ${e} -> ${t}`)),!0}catch(r){console.error("Error adding inventory mapping:",r);try{return o[e]=t,console.log(`Added mapping to memory fallback: ${e} -> ${t}`),!0}catch(e){return console.error("Error adding to memory fallback:",e),!1}}}async function m(e){try{if(i())return await a.get(`${s}${e}`)||null;return o[e]||null}catch(t){console.error("Error getting product handle from Redis:",t);try{return o[e]||null}catch(e){return console.error("Error getting from memory fallback:",e),null}}}async function p(e){try{if(i()){let t=a.pipeline();for(let{inventoryItemId:r,productHandle:n}of e)t.set(`${s}${r}`,n);await t.exec(),console.log(`Updated ${e.length} inventory mappings in Redis`)}else{for(let{inventoryItemId:t,productHandle:r}of e)o[t]=r;console.log(`Updated ${e.length} inventory mappings in memory`)}return!0}catch(t){console.error("Error updating inventory mappings in Redis:",t);try{for(let{inventoryItemId:t,productHandle:r}of e)o[t]=r;return console.log(`Updated ${e.length} inventory mappings in memory fallback`),!0}catch(e){return console.error("Error updating in memory fallback:",e),!1}}}async function u(){return await l()}async function g(){try{if(i()){let e=await a.keys(`${s}*`);e.length>0&&await a.del(...e),console.log("Cleared all inventory mappings from Redis")}else Object.keys(o).forEach(e=>{delete o[e]}),console.log("Cleared all inventory mappings from memory");return!0}catch(e){return console.error("Error clearing inventory mappings:",e),!1}}},45107:(e,t,r)=>{"use strict";function n(e="INR"){switch(e){case"INR":return"₹";case"USD":return"$";case"EUR":return"€";case"GBP":return"\xa3";default:return e}}r.d(t,{jK:()=>n}),r(92079)},51223:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var n=r(41135),s=r(31009);function a(...e){return(0,s.m6)((0,n.W)(e))}},32606:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\collection\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8948,1057,2481,325,8578,5436,5725,5916,3471],()=>r(11443));module.exports=n})();