"use strict";(()=>{var e={};e.id=8665,e.ids=[8665],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},93687:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>y,patchFetch:()=>m,requestAsyncStorage:()=>c,routeModule:()=>p,serverHooks:()=>l,staticGenerationAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{POST:()=>u});var o=t(49303),a=t(88716),n=t(60670),i=t(87070);async function u(){try{let e="rzp_live_H1Iyl4j48eSFYj",r=process.env.RAZORPAY_KEY_SECRET;if(!e||!r)return i.NextResponse.json({success:!1,error:"Razorpay credentials not configured"},{status:500});let t={amount:100,currency:"INR",receipt:`test_receipt_${Date.now()}`,notes:{test:"true",purpose:"connection_test"}},s=Buffer.from(`${e}:${r}`).toString("base64"),o=await fetch("https://api.razorpay.com/v1/orders",{method:"POST",headers:{Authorization:`Basic ${s}`,"Content-Type":"application/json"},body:JSON.stringify(t)});if(!o.ok){let e=await o.json();return i.NextResponse.json({success:!1,error:"Failed to create test order",details:e,status:o.status},{status:o.status})}let a=await o.json();return i.NextResponse.json({success:!0,message:"Test order created successfully",order:{id:a.id,amount:a.amount,currency:a.currency,receipt:a.receipt,status:a.status,created_at:a.created_at}})}catch(e){return console.error("Razorpay test order error:",e),i.NextResponse.json({success:!1,error:"Failed to create test order",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/razorpay/test-order/route",pathname:"/api/razorpay/test-order",filename:"route",bundlePath:"app/api/razorpay/test-order/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\razorpay\\test-order\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:c,staticGenerationAsyncStorage:d,serverHooks:l}=p,y="/api/razorpay/test-order/route";function m(){return(0,n.patchFetch)({serverHooks:l,staticGenerationAsyncStorage:d})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,5972],()=>t(93687));module.exports=s})();