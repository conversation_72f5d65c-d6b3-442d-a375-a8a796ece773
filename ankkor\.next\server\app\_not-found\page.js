"use strict";(()=>{var e={};e.id=7409,e.ids=[7409],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},68477:(e,r,o)=>{o.r(r),o.d(r,{GlobalError:()=>i.Z,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>c,tree:()=>s}),o(35866),o(12523),o(11360),o(7629),o(11930);var n=o(23191),t=o(88716),i=o(43315),a=o(95231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);o.d(r,l);let s=["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(o.bind(o,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(o.bind(o,11360)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(o.bind(o,7629)),"E:\\ankkorwoo\\ankkor\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(o.bind(o,11930)),"E:\\ankkorwoo\\ankkor\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(o.bind(o,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],d=[],u="/_not-found/page",p={require:o,loadChunk:()=>Promise.resolve()},c=new n.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:s}})},35866:(e,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return i}}),o(53370);let n=o(19510);o(71159);let t={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{display:"inline-block"},h1:{display:"inline-block",margin:"0 20px 0 0",padding:"0 23px 0 0",fontSize:24,fontWeight:500,verticalAlign:"top",lineHeight:"49px"},h2:{fontSize:14,fontWeight:400,lineHeight:"49px",margin:0}};function i(){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("title",{children:"404: This page could not be found."}),(0,n.jsx)("div",{style:t.error,children:(0,n.jsxs)("div",{children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}),(0,n.jsx)("h1",{className:"next-error-h1",style:t.h1,children:"404"}),(0,n.jsx)("div",{style:t.desc,children:(0,n.jsx)("h2",{style:t.h2,children:"This page could not be found."})})]})})]})}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},53370:(e,r,o)=>{function n(e){return e&&e.__esModule?e:{default:e}}o.r(r),o.d(r,{_:()=>n,_interop_require_default:()=>n})}};var r=require("../../webpack-runtime.js");r.C(e);var o=e=>r(r.s=e),n=r.X(0,[8948,1057,5436],()=>o(68477));module.exports=n})();