"use strict";(()=>{var e={};e.id=9984,e.ids=[9984],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},92914:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>g,patchFetch:()=>S,requestAsyncStorage:()=>d,routeModule:()=>p,serverHooks:()=>R,staticGenerationAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{DELETE:()=>v,GET:()=>l,POST:()=>u});var o=t(49303),a=t(88716),n=t(60670),i=t(87070),c=t(95307);async function u(e){try{let r=await e.json(),{productId:t,quantity:s,userId:o,variationId:a,cartId:n,action:u}=r;switch(u){case"create":if(!t||!s||!o)return i.NextResponse.json({error:"Missing required fields: productId, quantity, userId"},{status:400});let l=await (0,c.eI)(t,parseInt(s),o,a,n);if(l.success)return i.NextResponse.json({success:!0,reservation:l.reservation,message:`Reserved ${s} items for 15 minutes`});return i.NextResponse.json({error:l.error,availableStock:l.availableStock},{status:400});case"confirm":if(!r.reservationId)return i.NextResponse.json({error:"Missing reservationId"},{status:400});let v=await (0,c.fr)(r.reservationId);return i.NextResponse.json({success:v,message:v?"Reservation confirmed":"Failed to confirm reservation"});case"release":if(!r.reservationId)return i.NextResponse.json({error:"Missing reservationId"},{status:400});let p=await (0,c.O$)(r.reservationId);return i.NextResponse.json({success:p,message:p?"Reservation released":"Failed to release reservation"});case"cleanup":let d=await (0,c.on)();return i.NextResponse.json({success:!0,cleanedUp:d,message:`Cleaned up ${d} expired reservations`});default:return i.NextResponse.json({error:"Invalid action. Use: create, confirm, release, or cleanup"},{status:400})}}catch(e){return console.error("Error in reservations API:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function l(e){try{let{searchParams:r}=new URL(e.url),t=r.get("action"),s=r.get("userId"),o=r.get("productId"),a=r.get("variationId");switch(t){case"user_reservations":if(!s)return i.NextResponse.json({error:"Missing userId parameter"},{status:400});let n=await (0,c.ag)(s);return i.NextResponse.json({success:!0,reservations:n,count:n.length});case"check_stock":if(!o)return i.NextResponse.json({error:"Missing productId parameter"},{status:400});let u=await (0,c.mV)(o,a||void 0);return i.NextResponse.json({success:u.success,availableStock:u.availableStock,totalStock:u.totalStock,reservedStock:u.reservedStock});default:return i.NextResponse.json({message:"Stock Reservation API",endpoints:{"POST /api/reservations":{actions:["create","confirm","release","cleanup"],description:"Manage stock reservations"},"GET /api/reservations?action=user_reservations&userId=X":"Get user reservations","GET /api/reservations?action=check_stock&productId=X":"Check available stock"}})}}catch(e){return console.error("Error in reservations GET API:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function v(e){try{let{searchParams:r}=new URL(e.url),t=r.get("reservationId");if(!t)return i.NextResponse.json({error:"Missing reservationId parameter"},{status:400});let s=await (0,c.O$)(t);return i.NextResponse.json({success:s,message:s?"Reservation released":"Failed to release reservation"})}catch(e){return console.error("Error in reservations DELETE API:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/reservations/route",pathname:"/api/reservations",filename:"route",bundlePath:"app/api/reservations/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\reservations\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:d,staticGenerationAsyncStorage:f,serverHooks:R}=p,g="/api/reservations/route";function S(){return(0,n.patchFetch)({serverHooks:R,staticGenerationAsyncStorage:f})}},95307:(e,r,t)=>{t.d(r,{O$:()=>d,ag:()=>v,eI:()=>c,fr:()=>p,mV:()=>u,on:()=>R});var s=t(94868);let o=process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN?new s.s({url:process.env.UPSTASH_REDIS_REST_URL,token:process.env.UPSTASH_REDIS_REST_TOKEN}):null,a={DURATION_MINUTES:15,MAX_RESERVATIONS_PER_USER:10};function n(e,r){return r?`reservation:product:${e}:variation:${r}`:`reservation:product:${e}`}function i(e){return`user_reservations:${e}`}async function c(e,r,t,s,c){if(!o)return{success:!1,error:"Reservation service unavailable"};try{if((await v(t)).length>=a.MAX_RESERVATIONS_PER_USER)return{success:!1,error:"Too many active reservations"};let l=await u(e,s);if(!l.success||(l.availableStock||0)<r)return{success:!1,error:"Insufficient stock available",availableStock:l.availableStock};let p=new Date,d=new Date(p.getTime()+6e4*a.DURATION_MINUTES),f={id:`res_${Date.now()}_${Math.random().toString(36).substring(2,15)}`,productId:e,variationId:s,quantity:r,userId:t,reservedAt:p.toISOString(),expiresAt:d.toISOString(),status:"active",cartId:c},R=`${n(e,s)}:${f.id}`;await o.set(R,f,{ex:60*a.DURATION_MINUTES});let g=i(t);return await o.sadd(g,f.id),await o.expire(g,60*a.DURATION_MINUTES),console.log(`Created stock reservation: ${f.id} for product ${e} (${r} items)`),{success:!0,reservation:f}}catch(e){return console.error("Error creating stock reservation:",e),{success:!1,error:"Failed to create reservation"}}}async function u(e,r){if(!o)return{success:!1};try{let t=await f(e,r);if(null===t)return{success:!1};let s=await l(e,r);return{success:!0,availableStock:Math.max(0,t-s),totalStock:t,reservedStock:s}}catch(e){return console.error("Error checking available stock:",e),{success:!1}}}async function l(e,r){if(!o)return 0;try{let t=`${n(e,r)}:*`,s=await o.keys(t),a=0;for(let e of s){let r=await o.get(e);r&&"active"===r.status&&(a+=r.quantity)}return a}catch(e){return console.error("Error getting reserved stock:",e),0}}async function v(e){if(!o)return[];try{let r=i(e),t=await o.smembers(r),s=[];for(let e of t)for(let r of(await o.keys(`reservation:product:*:${e}`))){let e=await o.get(r);e&&"active"===e.status&&s.push(e)}return s}catch(e){return console.error("Error getting user reservations:",e),[]}}async function p(e){if(!o)return!1;try{for(let r of(await o.keys(`reservation:product:*:${e}`))){let t=await o.get(r);if(t)return t.status="confirmed",await o.set(r,t,{ex:86400}),console.log(`Confirmed reservation: ${e}`),!0}return!1}catch(e){return console.error("Error confirming reservation:",e),!1}}async function d(e){if(!o)return!1;try{for(let r of(await o.keys(`reservation:product:*:${e}`)))await o.del(r),console.log(`Released reservation: ${e}`);return!0}catch(e){return console.error("Error releasing reservation:",e),!1}}async function f(e,r){try{let t=`https://ankkorwoo.vercel.app/api/products/${e}/stock${r?`?variation_id=${r}`:""}`,s=await fetch(t);if(!s.ok)return null;return(await s.json()).stockQuantity||0}catch(e){return console.error("Error fetching stock from WooCommerce:",e),null}}async function R(){if(!o)return 0;try{let e=await o.keys("reservation:product:*"),r=0;for(let t of e){let e=await o.get(t);e&&new Date(e.expiresAt)<new Date&&(await o.del(t),r++)}return console.log(`Cleaned up ${r} expired reservations`),r}catch(e){return console.error("Error cleaning up expired reservations:",e),0}}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,5972,4766,4868],()=>t(92914));module.exports=s})();