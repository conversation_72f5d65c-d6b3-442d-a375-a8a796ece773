"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6305],{6305:function(e,t,r){r.r(t),r.d(t,{default:function(){return E}});var a=r(57437),s=r(2265),l=r(33145),c=r(99376),i=r(48131),n=r(43886),o=r(42449),d=r(32489),m=r(76858),u=r(63639),x=r(82431);let h=(0,r(39763).Z)("WifiOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 4.17-2.65",key:"11utq1"}],["path",{d:"M10.66 5c4.01-.36 8.14.9 11.34 3.76",key:"hxefdu"}],["path",{d:"M16.85 11.25a10 10 0 0 1 2.22 1.68",key:"q734kn"}],["path",{d:"M5 13a10 10 0 0 1 5.24-2.76",key:"piq4yl"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]);var p=r(18930),f=r(21047),g=r(99397),y=r(87758),j=r(82372),b=r(29658),N=r(70597),v=r(12381),w=r(14362),k=r(18686),C=r(86366);let I=e=>{var t;let{item:r,updateQuantity:s,removeFromCart:c,formatPrice:i}=e;return(0,a.jsxs)("li",{className:"flex gap-4 py-4 border-b",children:[(0,a.jsx)("div",{className:"relative h-20 w-20 bg-gray-100 flex-shrink-0",children:(null===(t=r.image)||void 0===t?void 0:t.url)&&(0,a.jsx)(l.default,{src:r.image.url,alt:r.image.altText||r.name,fill:!0,sizes:"80px",className:"object-cover",priority:!1})}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,a.jsx)("h4",{className:"text-sm font-medium line-clamp-2",children:r.name}),r.attributes&&r.attributes.length>0&&(0,a.jsx)("div",{className:"mt-1 text-xs text-gray-500",children:r.attributes.map((e,t)=>(0,a.jsxs)("span",{children:[e.name,": ",e.value,t<r.attributes.length-1?", ":""]},e.name))}),(0,a.jsx)("div",{className:"mt-1 text-sm font-medium",children:r.price&&"string"==typeof r.price&&r.price.toString().includes("₹")?r.price:"".concat(N.J6).concat(i(r.price||"0"))}),(0,a.jsxs)("div",{className:"mt-2 flex items-center gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center border border-gray-300",children:[(0,a.jsx)("button",{onClick:()=>{r.quantity>1&&s(r.id,r.quantity-1)},disabled:r.quantity<=1,className:"px-2 py-1 hover:bg-gray-100 disabled:opacity-50","aria-label":"Decrease quantity",children:(0,a.jsx)(f.Z,{className:"h-3 w-3"})}),(0,a.jsx)("span",{className:"px-2 py-1 text-sm",children:r.quantity}),(0,a.jsx)("button",{onClick:()=>{s(r.id,r.quantity+1)},className:"px-2 py-1 hover:bg-gray-100","aria-label":"Increase quantity",children:(0,a.jsx)(g.Z,{className:"h-3 w-3"})})]}),(0,a.jsx)("button",{onClick:()=>{c(r.id)},className:"p-1 hover:bg-gray-100 rounded-full","aria-label":"Remove item",children:(0,a.jsx)(p.Z,{className:"h-4 w-4 text-gray-500"})})]})]})]})};var E=()=>{let{isOpen:e,toggleCart:t}=(0,k.j)(),[r,l]=(0,s.useState)(!1),[f,g]=(0,s.useState)(null),[N,E]=(0,s.useState)(!1),[q,Z]=(0,s.useState)(!1),[F,S]=(0,s.useState)({}),P=(0,c.useRouter)(),{isAuthenticated:z,user:M,token:D,isLoading:R}=(0,w.a)(),T=(0,y.useLocalCartStore)(),{items:Q,itemCount:L,removeCartItem:Y,updateCartItem:_,clearCart:A,error:O,setError:W}=T,$=e=>{try{let t="string"==typeof e?parseFloat(e):e;if(isNaN(t))return"0.00";return t.toFixed(2)}catch(e){return console.error("Error formatting price:",e),"0.00"}};(0,s.useEffect)(()=>{console.log("Cart items:",Q),console.log("Cart subtotal calculation:");let e=0;Q.forEach(t=>{let r=0,a=(r="string"==typeof t.price?parseFloat(t.price.replace(/[₹$€£]/g,"").trim().replace(/,/g,"")):t.price)*t.quantity;console.log("Item: ".concat(t.name,", Price: ").concat(t.price,", Cleaned price: ").concat(r,", Quantity: ").concat(t.quantity,", Total: ").concat(a)),e+=a}),console.log("Manual subtotal calculation: ".concat(e)),console.log("Store subtotal calculation: ".concat(T.subtotal()))},[Q,T]);let J=$(Q.reduce((e,t)=>{let r=0;return isNaN(r="string"==typeof t.price?parseFloat(t.price.replace(/[₹$€£]/g,"").trim().replace(/,/g,"")):t.price)?(console.warn("Invalid price for item ".concat(t.id,": ").concat(t.price)),e):e+r*t.quantity},0));(0,s.useEffect)(()=>{(async()=>{let e={};for(let r of Q)try{if(!F[r.productId])try{let t=await j.gk(r.productId);(null==t?void 0:t.slug)?e[r.productId]=t.slug:(console.warn("Product with ID ".concat(r.productId," has no slug")),e[r.productId]="product-not-found")}catch(a){var t;console.error("Failed to load handle for product ".concat(r.productId,":"),a),e[r.productId]="product-not-found",(null===(t=a.message)||void 0===t?void 0:t.includes("No product ID was found"))&&console.warn("Product with ID ".concat(r.productId," not found in WooCommerce, but keeping in cart"))}}catch(e){console.error("Error processing product ".concat(r.productId,":"),e)}Object.keys(e).length>0&&S(t=>({...t,...e}))})()},[Q,F]);let U=async(e,t)=>{E(!0);try{await _(e,t),C.Q5.itemUpdated(e,t,"Item quantity updated")}catch(t){console.error("Error updating quantity:",t);let e=t instanceof Error?t.message:"Failed to update quantity";W(e),C.Cc.show(e,"error")}finally{E(!1)}},B=async e=>{try{await Y(e),C.Q5.itemRemoved(e,"Item removed from cart")}catch(t){console.error("Error removing item:",t);let e=t instanceof Error?t.message:"Failed to remove item";W(e),C.Cc.show(e,"error")}},G=async()=>{try{await A(),C.Q5.cleared("Cart cleared")}catch(t){console.error("Error clearing cart:",t);let e=t instanceof Error?t.message:"Failed to clear cart";C.Cc.show(e,"error")}},H=async()=>{l(!0),g(null);try{if(0===Q.length)throw Error("Your cart is empty");t(),P.push("/checkout"),setTimeout(()=>{l(!1)},1e3)}catch(e){console.error("Checkout error:",e),g(e instanceof Error?e.message:"An error occurred during checkout"),C.Cc.show(e instanceof Error?e.message:"An error occurred during checkout","error"),l(!1)}},K=async()=>{Z(!0),g(null);try{await H()}catch(e){console.error("Retry error:",e),g(e instanceof Error?e.message:"Retry failed")}finally{Z(!1)}},V=Q.length>0;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.M,{children:e&&(0,a.jsx)(n.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:e=>{e.preventDefault(),e.stopPropagation(),console.log("Cart backdrop clicked"),t()},className:"cart-overlay fixed inset-0 bg-black/50","aria-hidden":"true"})}),(0,a.jsx)(i.M,{children:e&&(0,a.jsxs)(n.E.div,{initial:{x:"100%"},animate:{x:0},exit:{x:"100%"},transition:{type:"tween",ease:"easeInOut",duration:.3},className:"cart-sidebar fixed top-0 right-0 h-full w-full max-w-md bg-white shadow-xl flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",children:[(0,a.jsxs)("h2",{className:"text-lg font-medium flex items-center gap-2",children:[(0,a.jsx)(o.Z,{className:"h-5 w-5"}),"Your Cart"]}),(0,a.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),console.log("Cart close button clicked"),t()},className:"p-2 hover:bg-gray-100 rounded-full transition-colors z-10 relative","aria-label":"Close cart",type:"button",children:(0,a.jsx)(d.Z,{className:"h-5 w-5"})})]}),(0,a.jsxs)("div",{className:"flex-1 overflow-y-auto p-4",children:[!V&&!O&&(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center h-full text-center p-4",children:[(0,a.jsx)(o.Z,{className:"h-12 w-12 text-gray-300 mb-2"}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-1",children:"Your cart is empty"}),(0,a.jsx)("p",{className:"text-gray-500 mb-4",children:"Looks like you haven't added any items yet."}),(0,a.jsxs)(v.z,{onClick:t,className:"flex items-center gap-2",children:["Continue Shopping",(0,a.jsx)(m.Z,{className:"h-4 w-4"})]})]}),O&&(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center h-full text-center p-4",children:[(0,a.jsx)(u.Z,{className:"h-12 w-12 text-red-500 mb-2"}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-1",children:"Something went wrong"}),(0,a.jsx)("p",{className:"text-gray-500 mb-4",children:O}),(0,a.jsxs)(v.z,{onClick:()=>W(null),className:"flex items-center gap-2",variant:"outline",children:[(0,a.jsx)(x.Z,{className:"h-4 w-4"}),"Try Again"]})]}),V&&(0,a.jsx)("ul",{className:"divide-y",children:Q.map(e=>(0,a.jsx)(I,{item:e,updateQuantity:U,removeFromCart:B,formatPrice:$},e.id))})]}),(0,a.jsxs)("div",{className:"border-t p-4 space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between font-medium",children:[(0,a.jsx)("span",{children:"Subtotal"}),(0,a.jsxs)("span",{children:["₹",J]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-lg font-semibold",children:[(0,a.jsx)("span",{children:"Total"}),(0,a.jsxs)("span",{children:["₹",J]})]}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)(v.z,{onClick:H,disabled:!V||N||r,className:"w-full h-12 bg-[#2c2c27] text-[#f4f3f0] hover:bg-[#3d3d35] font-medium transition-colors",size:"lg",children:r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-2 border-[#f4f3f0] border-t-transparent mr-2"}),"Processing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.Z,{className:"h-4 w-4 mr-2"}),"Proceed to Checkout"]})})}),f&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 p-3 rounded-md",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(u.Z,{className:"h-5 w-5 text-red-500 mt-0.5 mr-2 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-red-700",children:f}),(0,a.jsx)("button",{onClick:K,disabled:q,className:"mt-2 text-xs flex items-center text-red-700 hover:text-red-800",children:q?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.Z,{className:"h-3 w-3 animate-spin mr-1"}),"Retrying..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.Z,{className:"h-3 w-3 mr-1"}),"Try again"]})})]})]})}),!navigator.onLine&&(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 p-3 rounded-md",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h,{className:"h-4 w-4 text-yellow-500 mr-2"}),(0,a.jsx)("p",{className:"text-xs text-yellow-700",children:"You appear to be offline. Please check your internet connection."})]})})]}),(0,a.jsxs)(v.z,{onClick:G,variant:"ghost",size:"sm",disabled:r||N||!V,className:"w-full text-center text-[#8a8778] hover:text-[#2c2c27] hover:bg-[#f4f3f0] mt-2 transition-colors",children:[(0,a.jsx)(p.Z,{className:"h-3 w-3 mr-1"}),"Clear Cart"]})]})})]})}}}]);