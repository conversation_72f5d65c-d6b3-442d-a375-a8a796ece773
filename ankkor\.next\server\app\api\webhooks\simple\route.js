"use strict";(()=>{var e={};e.id=8734,e.ids=[8734],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},7398:(e,t,o)=>{o.r(t),o.d(t,{originalPathname:()=>h,patchFetch:()=>g,requestAsyncStorage:()=>l,routeModule:()=>c,serverHooks:()=>d,staticGenerationAsyncStorage:()=>m});var r={};o.r(r),o.d(r,{GET:()=>u,POST:()=>p});var s=o(49303),n=o(88716),i=o(60670),a=o(87070);async function p(e){console.log("=== Simple Webhook Test ===");try{let t=await e.text(),o=Object.fromEntries(e.headers.entries());return console.log("Headers received:",o),console.log("Body length:",t.length),console.log("Body preview:",t.substring(0,100)),a.NextResponse.json({success:!0,message:"Simple webhook received successfully",timestamp:new Date().toISOString(),bodyLength:t.length,hasSignature:!!o["x-wc-webhook-signature"]})}catch(e){return console.error("Simple webhook error:",e),a.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Unknown error",timestamp:new Date().toISOString()})}}async function u(){return a.NextResponse.json({message:"Simple webhook test endpoint",status:"active",timestamp:new Date().toISOString()})}let c=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/webhooks/simple/route",pathname:"/api/webhooks/simple",filename:"route",bundlePath:"app/api/webhooks/simple/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\webhooks\\simple\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:l,staticGenerationAsyncStorage:m,serverHooks:d}=c,h="/api/webhooks/simple/route";function g(){return(0,i.patchFetch)({serverHooks:d,staticGenerationAsyncStorage:m})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),r=t.X(0,[8948,5972],()=>o(7398));module.exports=r})();