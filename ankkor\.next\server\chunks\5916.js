"use strict";exports.id=5916,exports.ids=[5916],exports.modules={67427:(t,e,r)=>{r.d(e,{Z:()=>i});let i=(0,r(76557).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},34565:(t,e,r)=>{r.d(e,{Z:()=>i});let i=(0,r(76557).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},96040:(t,e,r)=>{r.a(t,async(t,i)=>{try{r.d(e,{Y:()=>d});var a=r(60114),o=r(85251),n=r(15725),s=t([n]);n=(s.then?(await s)():s)[0];let l={getItem:t=>null,setItem:(t,e)=>{},removeItem:t=>{}},c=(t,e)=>{try{if(!e||!e.lines){console.error("Invalid normalized cart data",e);return}let r=e.lines.reduce((t,e)=>t+(e.quantity||0),0),i=e.lines.map(t=>({id:t.id,variantId:t.merchandise.id,productId:t.merchandise.product.id,title:t.merchandise.product.title,handle:t.merchandise.product.handle,image:t.merchandise.product.image?.url||"",price:t.merchandise.price,quantity:t.quantity,currencyCode:t.merchandise.currencyCode}));t({items:i,subtotal:e.cost.subtotalAmount.amount,total:e.cost.totalAmount.amount,currencyCode:e.cost.totalAmount.currencyCode,itemCount:r,checkoutUrl:e.checkoutUrl,isLoading:!1})}catch(e){console.error("Error updating cart state:",e),t({items:[],subtotal:"0.00",total:"0.00",itemCount:0,isLoading:!1})}};(0,a.Ue)()((0,o.tJ)((t,e)=>({cartId:null,items:[],isOpen:!1,isLoading:!1,subtotal:"0.00",total:"0.00",currencyCode:"USD",itemCount:0,checkoutUrl:null,initializationInProgress:!1,initializationError:null,openCart:()=>t({isOpen:!0}),closeCart:()=>t({isOpen:!1}),toggleCart:()=>t(t=>({isOpen:!t.isOpen})),initCart:async()=>{let r=e();if(r.initializationInProgress)return console.log("Cart initialization already in progress, skipping"),null;t({isLoading:!0,initializationInProgress:!0,initializationError:null});try{if(r.cartId)try{let e=await (0,n.dv)();if(e)return t({isLoading:!1,initializationInProgress:!1}),e}catch(t){console.log("Existing cart validation failed, creating new cart")}let e=await (0,n.Bk)();if(e&&e.id)return t({cartId:e.id,checkoutUrl:e.checkoutUrl,isLoading:!1,initializationInProgress:!1}),console.log("Cart initialized with ID:",e.id),e;throw Error("Failed to create cart: No cart ID returned")}catch(e){return console.error("Failed to initialize cart:",e),t({isLoading:!1,initializationInProgress:!1,initializationError:e instanceof Error?e.message:"Unknown error initializing cart"}),null}},addItem:async r=>{t({isLoading:!0});try{if(!r.variantId)throw console.error("Cannot add item to cart: Missing variant ID",r),t({isLoading:!1}),Error("Missing variant ID for item");let i=e().cartId;if(!i){console.log("Cart not initialized, creating a new cart...");let t=await (0,n.Bk)();if(t&&t.id)console.log("New cart created:",t.id),i=t.id;else throw Error("Failed to initialize cart")}if(!i)throw Error("Failed to initialize cart: No cart ID available");console.log(`Adding item to cart: ${r.title} (${r.variantId}), quantity: ${r.quantity}`);try{let e=await (0,n.Xq)(i,[{merchandiseId:r.variantId,quantity:r.quantity||1}]);if(!e)throw Error("Failed to add item to cart: No cart returned");let a=(0,n.Id)(e);c(t,a),t({isOpen:!0}),console.log(`Item added to cart successfully. Cart now has ${a.lines.length} items.`)}catch(t){if(console.error("Shopify API error when adding to cart:",t),t instanceof Error)throw Error(`Failed to add item to cart: ${t.message}`);throw Error("Failed to add item to cart: Unknown API error")}}catch(e){throw console.error("Failed to add item to cart:",e),t({isLoading:!1}),e}},updateItem:async(r,i)=>{let a=e();t({isLoading:!0});try{if(!a.cartId)throw Error("Cart not initialized");if(console.log(`Updating item in cart: ${r}, new quantity: ${i}`),i<=0)return console.log(`Quantity is ${i}, removing item from cart`),e().removeItem(r);let o=await (0,n.xu)(a.cartId,[{id:r,quantity:i}]);if(!o)throw Error("Failed to update item: No cart returned");let s=(0,n.Id)(o);c(t,s),console.log(`Item updated successfully. Cart now has ${s.lines.length} items.`)}catch(e){throw console.error("Failed to update item in cart:",e),t({isLoading:!1}),e}},removeItem:async r=>{let i=e();t({isLoading:!0});try{if(!i.cartId)throw console.error("Cannot remove item: Cart not initialized"),Error("Cart not initialized");console.log(`Removing item from cart: ${r}`);let e=[...i.items],a=e.find(t=>t.id===r);a?console.log(`Removing "${a.title}" (${a.variantId}) from cart`):console.warn(`Item with ID ${r} not found in cart`);let o=await (0,n.h2)(i.cartId,[r]);if(!o)throw console.error("Failed to remove item: No cart returned from Shopify"),Error("Failed to remove item: No cart returned");let s=(0,n.Id)(o),l=s.lines.map(t=>({id:t.id,title:t.merchandise.product.title}));console.log("Cart before removal:",e.length,"items"),console.log("Cart after removal:",l.length,"items"),e.length===l.length&&console.warn("Item count did not change after removal operation"),c(t,s),console.log(`Item removed successfully. Cart now has ${s.lines.length} items.`)}catch(e){throw console.error("Failed to remove item from cart:",e),t({isLoading:!1}),e}},clearCart:async()=>{e(),t({isLoading:!0});try{console.log("Clearing cart and creating a new one");let e=await (0,n.Bk)();if(!e)throw Error("Failed to create new cart");t({cartId:e.id,items:[],subtotal:"0.00",total:"0.00",itemCount:0,checkoutUrl:e.checkoutUrl,isLoading:!1}),console.log("Cart cleared successfully. New cart ID:",e.id)}catch(e){throw console.error("Failed to clear cart:",e),t({isLoading:!1}),e}}}),{name:"ankkor-cart",storage:(0,o.FL)(()=>l),version:1,partialize:t=>({cartId:t.cartId,items:t.items,subtotal:t.subtotal,total:t.total,currencyCode:t.currencyCode,itemCount:t.itemCount,checkoutUrl:t.checkoutUrl})}));let d=(0,a.Ue)()((0,o.tJ)((t,e)=>({items:[],isLoading:!1,addToWishlist:e=>{t(t=>t.items.some(t=>t.id===e.id)?t:{items:[...t.items,e]})},removeFromWishlist:e=>{t(t=>({items:t.items.filter(t=>t.id!==e)}))},clearWishlist:()=>{t({items:[]})},isInWishlist:t=>e().items.some(e=>e.id===t)}),{name:"ankkor-wishlist",storage:(0,o.FL)(()=>l),partialize:t=>({items:t.items}),skipHydration:!0}));i()}catch(t){i(t)}})},40381:(t,e,r)=>{r.d(e,{x7:()=>tc,Am:()=>A});var i,a=r(17577);let o={data:""},n=t=>"object"==typeof window?((t?t.querySelector("#_goober"):window._goober)||Object.assign((t||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:t||o,s=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,l=/\/\*[^]*?\*\/|  +/g,c=/\n+/g,d=(t,e)=>{let r="",i="",a="";for(let o in t){let n=t[o];"@"==o[0]?"i"==o[1]?r=o+" "+n+";":i+="f"==o[1]?d(n,o):o+"{"+d(n,"k"==o[1]?"":e)+"}":"object"==typeof n?i+=d(n,e?e.replace(/([^,])+/g,t=>o.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,e=>/&/.test(e)?e.replace(/&/g,t):t?t+" "+e:e)):o):null!=n&&(o=/^--/.test(o)?o:o.replace(/[A-Z]/g,"-$&").toLowerCase(),a+=d.p?d.p(o,n):o+":"+n+";")}return r+(e&&a?e+"{"+a+"}":a)+i},u={},m=t=>{if("object"==typeof t){let e="";for(let r in t)e+=r+m(t[r]);return e}return t},p=(t,e,r,i,a)=>{let o=m(t),n=u[o]||(u[o]=(t=>{let e=0,r=11;for(;e<t.length;)r=101*r+t.charCodeAt(e++)>>>0;return"go"+r})(o));if(!u[n]){let e=o!==t?t:(t=>{let e,r,i=[{}];for(;e=s.exec(t.replace(l,""));)e[4]?i.shift():e[3]?(r=e[3].replace(c," ").trim(),i.unshift(i[0][r]=i[0][r]||{})):i[0][e[1]]=e[2].replace(c," ").trim();return i[0]})(t);u[n]=d(a?{["@keyframes "+n]:e}:e,r?"":"."+n)}let p=r&&u.g?u.g:null;return r&&(u.g=u[n]),((t,e,r,i)=>{i?e.data=e.data.replace(i,t):-1===e.data.indexOf(t)&&(e.data=r?t+e.data:e.data+t)})(u[n],e,i,p),n},g=(t,e,r)=>t.reduce((t,i,a)=>{let o=e[a];if(o&&o.call){let t=o(r),e=t&&t.props&&t.props.className||/^go/.test(t)&&t;o=e?"."+e:t&&"object"==typeof t?t.props?"":d(t,""):!1===t?"":t}return t+i+(null==o?"":o)},"");function f(t){let e=this||{},r=t.call?t(e.p):t;return p(r.unshift?r.raw?g(r,[].slice.call(arguments,1),e.p):r.reduce((t,r)=>Object.assign(t,r&&r.call?r(e.p):r),{}):r,n(e.target),e.g,e.o,e.k)}f.bind({g:1});let h,y,v,b=f.bind({k:1});function w(t,e){let r=this||{};return function(){let i=arguments;function a(o,n){let s=Object.assign({},o),l=s.className||a.className;r.p=Object.assign({theme:y&&y()},s),r.o=/ *go\d+/.test(l),s.className=f.apply(r,i)+(l?" "+l:""),e&&(s.ref=n);let c=t;return t[0]&&(c=s.as||t,delete s.as),v&&c[0]&&v(s),h(c,s)}return e?e(a):a}}var x=t=>"function"==typeof t,I=(t,e)=>x(t)?t(e):t,C=(()=>{let t=0;return()=>(++t).toString()})(),E=(()=>{let t;return()=>t})(),k=(t,e)=>{switch(e.type){case 0:return{...t,toasts:[e.toast,...t.toasts].slice(0,20)};case 1:return{...t,toasts:t.toasts.map(t=>t.id===e.toast.id?{...t,...e.toast}:t)};case 2:let{toast:r}=e;return k(t,{type:t.toasts.find(t=>t.id===r.id)?1:0,toast:r});case 3:let{toastId:i}=e;return{...t,toasts:t.toasts.map(t=>t.id===i||void 0===i?{...t,dismissed:!0,visible:!1}:t)};case 4:return void 0===e.toastId?{...t,toasts:[]}:{...t,toasts:t.toasts.filter(t=>t.id!==e.toastId)};case 5:return{...t,pausedAt:e.time};case 6:let a=e.time-(t.pausedAt||0);return{...t,pausedAt:void 0,toasts:t.toasts.map(t=>({...t,pauseDuration:t.pauseDuration+a}))}}},$=[],z={toasts:[],pausedAt:void 0},L=t=>{z=k(z,t),$.forEach(t=>{t(z)})},F={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},D=(t={})=>{let[e,r]=(0,a.useState)(z),i=(0,a.useRef)(z);(0,a.useEffect)(()=>(i.current!==z&&r(z),$.push(r),()=>{let t=$.indexOf(r);t>-1&&$.splice(t,1)}),[]);let o=e.toasts.map(e=>{var r,i,a;return{...t,...t[e.type],...e,removeDelay:e.removeDelay||(null==(r=t[e.type])?void 0:r.removeDelay)||(null==t?void 0:t.removeDelay),duration:e.duration||(null==(i=t[e.type])?void 0:i.duration)||(null==t?void 0:t.duration)||F[e.type],style:{...t.style,...null==(a=t[e.type])?void 0:a.style,...e.style}}});return{...e,toasts:o}},N=(t,e="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:e,ariaProps:{role:"status","aria-live":"polite"},message:t,pauseDuration:0,...r,id:(null==r?void 0:r.id)||C()}),O=t=>(e,r)=>{let i=N(e,t,r);return L({type:2,toast:i}),i.id},A=(t,e)=>O("blank")(t,e);A.error=O("error"),A.success=O("success"),A.loading=O("loading"),A.custom=O("custom"),A.dismiss=t=>{L({type:3,toastId:t})},A.remove=t=>L({type:4,toastId:t}),A.promise=(t,e,r)=>{let i=A.loading(e.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof t&&(t=t()),t.then(t=>{let a=e.success?I(e.success,t):void 0;return a?A.success(a,{id:i,...r,...null==r?void 0:r.success}):A.dismiss(i),t}).catch(t=>{let a=e.error?I(e.error,t):void 0;a?A.error(a,{id:i,...r,...null==r?void 0:r.error}):A.dismiss(i)}),t};var U=(t,e)=>{L({type:1,toast:{id:t,height:e}})},P=()=>{L({type:5,time:Date.now()})},j=new Map,M=1e3,q=(t,e=M)=>{if(j.has(t))return;let r=setTimeout(()=>{j.delete(t),L({type:4,toastId:t})},e);j.set(t,r)},S=t=>{let{toasts:e,pausedAt:r}=D(t);(0,a.useEffect)(()=>{if(r)return;let t=Date.now(),i=e.map(e=>{if(e.duration===1/0)return;let r=(e.duration||0)+e.pauseDuration-(t-e.createdAt);if(r<0){e.visible&&A.dismiss(e.id);return}return setTimeout(()=>A.dismiss(e.id),r)});return()=>{i.forEach(t=>t&&clearTimeout(t))}},[e,r]);let i=(0,a.useCallback)(()=>{r&&L({type:6,time:Date.now()})},[r]),o=(0,a.useCallback)((t,r)=>{let{reverseOrder:i=!1,gutter:a=8,defaultPosition:o}=r||{},n=e.filter(e=>(e.position||o)===(t.position||o)&&e.height),s=n.findIndex(e=>e.id===t.id),l=n.filter((t,e)=>e<s&&t.visible).length;return n.filter(t=>t.visible).slice(...i?[l+1]:[0,l]).reduce((t,e)=>t+(e.height||0)+a,0)},[e]);return(0,a.useEffect)(()=>{e.forEach(t=>{if(t.dismissed)q(t.id,t.removeDelay);else{let e=j.get(t.id);e&&(clearTimeout(e),j.delete(t.id))}})},[e]),{toasts:e,handlers:{updateHeight:U,startPause:P,endPause:i,calculateOffset:o}}},H=b`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,T=b`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Z=b`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,B=w("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${t=>t.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${H} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${T} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${t=>t.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Z} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,_=b`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,R=w("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${t=>t.secondary||"#e0e0e0"};
  border-right-color: ${t=>t.primary||"#616161"};
  animation: ${_} 1s linear infinite;
`,W=b`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,J=b`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Y=w("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${t=>t.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${W} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${J} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${t=>t.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,Q=w("div")`
  position: absolute;
`,V=w("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,X=b`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,G=w("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${X} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,K=({toast:t})=>{let{icon:e,type:r,iconTheme:i}=t;return void 0!==e?"string"==typeof e?a.createElement(G,null,e):e:"blank"===r?null:a.createElement(V,null,a.createElement(R,{...i}),"loading"!==r&&a.createElement(Q,null,"error"===r?a.createElement(B,{...i}):a.createElement(Y,{...i})))},tt=t=>`
0% {transform: translate3d(0,${-200*t}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,te=t=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*t}%,-1px) scale(.6); opacity:0;}
`,tr=w("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,ti=w("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,ta=(t,e)=>{let r=t.includes("top")?1:-1,[i,a]=E()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[tt(r),te(r)];return{animation:e?`${b(i)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${b(a)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},to=a.memo(({toast:t,position:e,style:r,children:i})=>{let o=t.height?ta(t.position||e||"top-center",t.visible):{opacity:0},n=a.createElement(K,{toast:t}),s=a.createElement(ti,{...t.ariaProps},I(t.message,t));return a.createElement(tr,{className:t.className,style:{...o,...r,...t.style}},"function"==typeof i?i({icon:n,message:s}):a.createElement(a.Fragment,null,n,s))});i=a.createElement,d.p=void 0,h=i,y=void 0,v=void 0;var tn=({id:t,className:e,style:r,onHeightUpdate:i,children:o})=>{let n=a.useCallback(e=>{if(e){let r=()=>{i(t,e.getBoundingClientRect().height)};r(),new MutationObserver(r).observe(e,{subtree:!0,childList:!0,characterData:!0})}},[t,i]);return a.createElement("div",{ref:n,className:e,style:r},o)},ts=(t,e)=>{let r=t.includes("top"),i=t.includes("center")?{justifyContent:"center"}:t.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:E()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${e*(r?1:-1)}px)`,...r?{top:0}:{bottom:0},...i}},tl=f`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,tc=({reverseOrder:t,position:e="top-center",toastOptions:r,gutter:i,children:o,containerStyle:n,containerClassName:s})=>{let{toasts:l,handlers:c}=S(r);return a.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...n},className:s,onMouseEnter:c.startPause,onMouseLeave:c.endPause},l.map(r=>{let n=r.position||e,s=ts(n,c.calculateOffset(r,{reverseOrder:t,gutter:i,defaultPosition:e}));return a.createElement(tn,{id:r.id,key:r.id,onHeightUpdate:c.updateHeight,className:r.visible?tl:"",style:s},"custom"===r.type?I(r.message,r):o?o(r):a.createElement(to,{toast:r,position:n}))}))}}};